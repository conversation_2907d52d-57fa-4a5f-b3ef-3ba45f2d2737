<template>
  <Dialog :open="show" @update:open="$emit('update:show', $event)">
    <DialogContent class="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle>Verifi<PERSON><PERSON> Dokumen</DialogTitle>
        <DialogDescription>
          {{ document.nama_file }} - {{ documentType?.name }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Document Preview -->
        <div class="border rounded-lg p-4">
          <h3 class="font-medium mb-3">Preview Dokumen</h3>
          
          <!-- Image Preview -->
          <div v-if="isImageFile" class="text-center">
            <img 
              :src="documentUrl" 
              :alt="document.nama_file"
              class="max-w-full max-h-96 mx-auto rounded border shadow"
              @error="imageLoadError = true"
            />
            <div v-if="imageLoadError" class="p-8 text-gray-500">
              <Icon name="imageOff" class="w-12 h-12 mx-auto mb-2" />
              <p>Tidak dapat memuat preview gambar</p>
            </div>
          </div>

          <!-- PDF Preview -->
          <div v-else-if="isPdfFile" class="text-center">
            <iframe 
              :src="documentUrl" 
              class="w-full h-96 border rounded"
              @error="pdfLoadError = true"
            ></iframe>
            <div v-if="pdfLoadError" class="p-8 text-gray-500">
              <Icon name="fileX" class="w-12 h-12 mx-auto mb-2" />
              <p>Tidak dapat memuat preview PDF</p>
            </div>
          </div>

          <!-- Other File Types -->
          <div v-else class="text-center p-8 text-gray-500">
            <Icon name="file" class="w-12 h-12 mx-auto mb-2" />
            <p>Preview tidak tersedia untuk jenis file ini</p>
            <Button @click="downloadDocument" variant="outline" class="mt-2">
              <Icon name="download" class="w-4 h-4 mr-2" />
              Download File
            </Button>
          </div>
        </div>

        <!-- Document Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="space-y-3">
            <div>
              <Label class="text-sm font-medium text-gray-500">Nama File</Label>
              <p class="text-sm">{{ document.nama_file }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Ukuran File</Label>
              <p class="text-sm">{{ formatFileSize(document.ukuran_file) }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Jenis File</Label>
              <p class="text-sm">{{ document.mime_type }}</p>
            </div>
          </div>
          <div class="space-y-3">
            <div>
              <Label class="text-sm font-medium text-gray-500">Tanggal Upload</Label>
              <p class="text-sm">{{ formatDate(document.created_at) }}</p>
            </div>
            <div>
              <Label class="text-sm font-medium text-gray-500">Status Saat Ini</Label>
              <Badge :variant="getStatusVariant(document.status_verifikasi)">
                {{ getStatusLabel(document.status_verifikasi) }}
              </Badge>
            </div>
            <div v-if="document.verified_by">
              <Label class="text-sm font-medium text-gray-500">Diverifikasi Oleh</Label>
              <p class="text-sm">{{ document.verified_by.nama_lengkap }}</p>
            </div>
          </div>
        </div>

        <!-- Current Verification Notes -->
        <div v-if="document.catatan_verifikasi" class="p-4 bg-blue-50 rounded border border-blue-200">
          <h4 class="text-sm font-medium text-blue-800 mb-1">Catatan Verifikasi Sebelumnya:</h4>
          <p class="text-sm text-blue-700">{{ document.catatan_verifikasi }}</p>
        </div>

        <!-- Verification Form -->
        <form @submit.prevent="handleVerification" class="space-y-4">
          <!-- Verification Action -->
          <div class="space-y-2">
            <Label for="action">Aksi Verifikasi</Label>
            <Select v-model="form.action" required>
              <SelectTrigger>
                <SelectValue placeholder="Pilih aksi verifikasi" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="approved">
                  <div class="flex items-center space-x-2">
                    <Icon name="check" class="w-4 h-4 text-green-600" />
                    <span>Setujui Dokumen</span>
                  </div>
                </SelectItem>
                <SelectItem value="rejected">
                  <div class="flex items-center space-x-2">
                    <Icon name="x" class="w-4 h-4 text-red-600" />
                    <span>Tolak Dokumen</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Verification Notes -->
          <div class="space-y-2">
            <Label for="notes">Catatan Verifikasi</Label>
            <Textarea
              id="notes"
              v-model="form.notes"
              placeholder="Tambahkan catatan verifikasi (wajib untuk penolakan)"
              rows="3"
              :required="form.action === 'rejected'"
            />
            <p v-if="form.action === 'rejected'" class="text-xs text-red-600">
              Catatan wajib diisi untuk penolakan dokumen
            </p>
          </div>

          <!-- Document Quality Checklist (for approved documents) -->
          <div v-if="form.action === 'approved'" class="space-y-3">
            <Label>Checklist Kualitas Dokumen</Label>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.quality_checks.readable" />
                <Label class="text-sm">Dokumen dapat dibaca dengan jelas</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.quality_checks.complete" />
                <Label class="text-sm">Informasi dalam dokumen lengkap</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.quality_checks.authentic" />
                <Label class="text-sm">Dokumen terlihat asli/tidak dimanipulasi</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.quality_checks.relevant" />
                <Label class="text-sm">Dokumen sesuai dengan jenis yang diminta</Label>
              </div>
            </div>
          </div>

          <!-- Rejection Reasons (for rejected documents) -->
          <div v-if="form.action === 'rejected'" class="space-y-3">
            <Label>Alasan Penolakan</Label>
            <div class="space-y-2">
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.rejection_reasons.unclear" />
                <Label class="text-sm">Dokumen tidak jelas/buram</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.rejection_reasons.incomplete" />
                <Label class="text-sm">Informasi tidak lengkap</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.rejection_reasons.wrong_type" />
                <Label class="text-sm">Jenis dokumen tidak sesuai</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.rejection_reasons.expired" />
                <Label class="text-sm">Dokumen sudah kadaluarsa</Label>
              </div>
              <div class="flex items-center space-x-2">
                <Checkbox v-model="form.rejection_reasons.suspicious" />
                <Label class="text-sm">Dokumen terindikasi palsu/dimanipulasi</Label>
              </div>
            </div>
          </div>

          <!-- Warning Message -->
          <div v-if="form.action" class="p-4 rounded-lg border">
            <div v-if="form.action === 'approved'" class="flex items-start space-x-2 text-green-700 bg-green-50 p-3 rounded">
              <Icon name="checkCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <p class="font-medium">Menyetujui Dokumen</p>
                <p class="text-sm text-green-600 mt-1">
                  Dokumen akan berstatus "Disetujui" dan dapat melanjutkan proses verifikasi.
                </p>
              </div>
            </div>
            <div v-else-if="form.action === 'rejected'" class="flex items-start space-x-2 text-red-700 bg-red-50 p-3 rounded">
              <Icon name="xCircle" class="w-5 h-5 mt-0.5 flex-shrink-0" />
              <div>
                <p class="font-medium">Menolak Dokumen</p>
                <p class="text-sm text-red-600 mt-1">
                  Dokumen akan berstatus "Ditolak" dan peserta perlu mengunggah ulang dokumen yang sesuai.
                </p>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <DialogFooter>
            <Button 
              type="button" 
              variant="outline" 
              @click="$emit('update:show', false)"
            >
              Batal
            </Button>
            <Button 
              @click="downloadDocument"
              type="button" 
              variant="outline"
            >
              <Icon name="download" class="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button 
              type="submit" 
              :disabled="!form.action || isSubmitting || (form.action === 'rejected' && !form.notes.trim())"
              :class="{
                'bg-green-600 hover:bg-green-700': form.action === 'approved',
                'bg-red-600 hover:bg-red-700': form.action === 'rejected'
              }"
            >
              <Icon 
                v-if="isSubmitting" 
                name="loader" 
                class="w-4 h-4 mr-2 animate-spin" 
              />
              <Icon 
                v-else-if="form.action === 'approved'" 
                name="check" 
                class="w-4 h-4 mr-2" 
              />
              <Icon 
                v-else-if="form.action === 'rejected'" 
                name="x" 
                class="w-4 h-4 mr-2" 
              />
              {{ getSubmitButtonText() }}
            </Button>
          </DialogFooter>
        </form>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  document: any
  documentType?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'verify': [data: { documentId: number; action: string; notes: string; metadata?: any }]
}>()

// Reactive data
const isSubmitting = ref(false)
const imageLoadError = ref(false)
const pdfLoadError = ref(false)
const form = ref({
  action: '',
  notes: '',
  quality_checks: {
    readable: false,
    complete: false,
    authentic: false,
    relevant: false
  },
  rejection_reasons: {
    unclear: false,
    incomplete: false,
    wrong_type: false,
    expired: false,
    suspicious: false
  }
})

// Computed
const documentUrl = computed(() => {
  return `/storage/${props.document.path_file}`
})

const isImageFile = computed(() => {
  return props.document.mime_type?.startsWith('image/')
})

const isPdfFile = computed(() => {
  return props.document.mime_type === 'application/pdf'
})

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    form.value = {
      action: '',
      notes: '',
      quality_checks: {
        readable: false,
        complete: false,
        authentic: false,
        relevant: false
      },
      rejection_reasons: {
        unclear: false,
        incomplete: false,
        wrong_type: false,
        expired: false,
        suspicious: false
      }
    }
    imageLoadError.value = false
    pdfLoadError.value = false
  }
})

// Methods
const handleVerification = async () => {
  if (!form.value.action) return
  if (form.value.action === 'rejected' && !form.value.notes.trim()) return

  isSubmitting.value = true
  
  try {
    const metadata = {
      quality_checks: form.value.action === 'approved' ? form.value.quality_checks : null,
      rejection_reasons: form.value.action === 'rejected' ? form.value.rejection_reasons : null
    }
    
    emit('verify', {
      documentId: props.document.id_dokumen,
      action: form.value.action,
      notes: form.value.notes,
      metadata
    })
  } finally {
    isSubmitting.value = false
  }
}

const downloadDocument = () => {
  window.open(`/admin/registration-verification/documents/${props.document.id_dokumen}/download`, '_blank')
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getSubmitButtonText = () => {
  if (isSubmitting.value) {
    return 'Memproses...'
  }
  
  if (form.value.action === 'approved') {
    return 'Setujui Dokumen'
  } else if (form.value.action === 'rejected') {
    return 'Tolak Dokumen'
  }
  
  return 'Verifikasi'
}
</script>
