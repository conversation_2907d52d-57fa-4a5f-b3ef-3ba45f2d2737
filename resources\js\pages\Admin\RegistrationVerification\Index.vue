<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi Pendaftaran" />

    <div class="space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <Heading title="Verifikasi Pendaftaran" />
        <div class="flex items-center space-x-3">
          <Button
            v-if="selectedRegistrations.length > 0"
            @click="showBulkVerifyModal = true"
            variant="outline"
            class="text-green-600 border-green-600 hover:bg-green-50"
          >
            <Icon name="check" class="w-4 h-4 mr-2" />
            Verifikasi Massal ({{ selectedRegistrations.length }})
          </Button>
        </div>
      </div>

      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-blue-500 rounded-full">
                <Icon name="fileText" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-blue-700">{{ stats.total_registrations }}</p>
                <p class="text-sm text-blue-600">Total Pendaftaran</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-yellow-500 rounded-full">
                <Icon name="clock" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-yellow-700">{{ stats.pending_verification }}</p>
                <p class="text-sm text-yellow-600">Menunggu Verifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-green-500 rounded-full">
                <Icon name="checkCircle" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-green-700">{{ stats.verified }}</p>
                <p class="text-sm text-green-600">Terverifikasi</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card class="islamic-shadow">
          <CardContent class="p-6">
            <div class="flex items-center space-x-2">
              <div class="p-3 bg-red-500 rounded-full">
                <Icon name="xCircle" class="h-6 w-6 text-white" />
              </div>
              <div>
                <p class="text-2xl font-bold text-red-700">{{ stats.rejected }}</p>
                <p class="text-sm text-red-600">Ditolak</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Filters and Search -->
      <Card class="islamic-shadow">
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
            <!-- Search -->
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="searchForm.search"
                placeholder="Nama, NIK, No. Pendaftaran..."
                @input="debouncedSearch"
              />
            </div>

            <!-- Status Filter -->
            <div>
              <Label for="status">Status</Label>
              <Select v-model="searchForm.status" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem
                    v-for="status in filterOptions.statuses"
                    :key="status.value"
                    :value="status.value"
                  >
                    {{ status.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Wilayah Filter -->
            <div>
              <Label for="wilayah">Wilayah</Label>
              <Select v-model="searchForm.wilayah" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Wilayah" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Wilayah</SelectItem>
                  <SelectItem
                    v-for="wilayah in filterOptions.wilayah"
                    :key="wilayah.id_wilayah"
                    :value="wilayah.id_wilayah.toString()"
                  >
                    {{ wilayah.nama_wilayah }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Cabang Lomba Filter -->
            <div>
              <Label for="cabang_lomba">Cabang Lomba</Label>
              <Select v-model="searchForm.cabang_lomba" @update:model-value="applyFilters">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Cabang" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Cabang</SelectItem>
                  <SelectItem
                    v-for="cabang in filterOptions.cabangLomba"
                    :key="cabang.id_cabang"
                    :value="cabang.id_cabang.toString()"
                  >
                    {{ cabang.nama_cabang }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Reset Filters -->
            <div class="flex items-end">
              <Button @click="resetFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Enhanced Registrations Table -->
      <Card class="islamic-shadow">
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <Checkbox
                      :checked="allSelected"
                      @update:checked="toggleSelectAll"
                    />
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pendaftaran
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Lomba
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white">
                <template v-for="registration in registrations.data" :key="registration.id_pendaftaran">
                  <!-- Main Registration Row -->
                  <tr
                    class="border-b hover:bg-gray-50 transition-colors"
                    :class="{ 'bg-blue-50': expandedRows.includes(registration.id_pendaftaran) }"
                  >
                    <td class="px-6 py-4">
                      <div class="flex items-center space-x-2">
                        <Checkbox
                          :checked="selectedRegistrations.includes(registration.id_pendaftaran)"
                          @update:checked="toggleRegistrationSelection(registration.id_pendaftaran)"
                        />
                        <Button
                          @click="toggleRowExpansion(registration.id_pendaftaran)"
                          size="sm"
                          variant="ghost"
                          class="p-1"
                        >
                          <Icon
                            :name="expandedRows.includes(registration.id_pendaftaran) ? 'chevronDown' : 'chevronRight'"
                            class="w-4 h-4"
                          />
                        </Button>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          {{ registration.peserta.nama_lengkap }}
                        </div>
                        <div class="text-sm text-gray-500">
                          NIK: {{ registration.peserta.nik }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ registration.peserta.wilayah?.nama_wilayah }}
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          {{ registration.nomor_pendaftaran }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ formatDate(registration.created_at) }}
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div>
                        <div class="text-sm font-medium text-gray-900">
                          {{ registration.golongan.nama_golongan }}
                        </div>
                        <div class="text-sm text-gray-500">
                          {{ registration.golongan.cabang_lomba.nama_cabang }}
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <Badge :variant="getStatusVariant(registration.status_pendaftaran)">
                        {{ getStatusLabel(registration.status_pendaftaran) }}
                      </Badge>
                    </td>
                    <td class="px-6 py-4">
                      <div class="space-y-2">
                        <!-- Overall Progress -->
                        <div class="flex items-center space-x-2">
                          <div class="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              :style="{ width: `${registration.verification_progress.overall_percentage}%` }"
                            ></div>
                          </div>
                          <span class="text-xs text-gray-500">
                            {{ registration.verification_progress.overall_percentage }}%
                          </span>
                        </div>
                        <!-- Document Status -->
                        <div class="flex items-center space-x-1 text-xs">
                          <Icon name="file" class="w-3 h-3 text-gray-400" />
                          <span class="text-green-600">{{ registration.document_status.approved }}</span>
                          <span class="text-gray-400">/</span>
                          <span class="text-gray-600">{{ registration.document_status.total }}</span>
                          <span class="text-gray-400">docs</span>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <div class="flex items-center space-x-1">
                        <!-- Quick Actions -->
                        <Button
                          v-if="canVerify(registration)"
                          @click="showInlineVerification(registration)"
                          size="sm"
                          variant="outline"
                          class="text-blue-600 border-blue-600 hover:bg-blue-50"
                        >
                          <Icon name="check" class="w-4 h-4 mr-1" />
                          Verifikasi
                        </Button>

                        <!-- Documents Button -->
                        <Button
                          @click="openDocumentModal(registration)"
                          size="sm"
                          variant="outline"
                          class="text-purple-600 border-purple-600 hover:bg-purple-50"
                        >
                          <Icon name="file" class="w-4 h-4 mr-1" />
                          Dokumen
                        </Button>

                        <!-- More Actions Dropdown -->
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button size="sm" variant="ghost" class="p-2">
                              <Icon name="moreVertical" class="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem @click="viewRegistration(registration)">
                              <Icon name="eye" class="w-4 h-4 mr-2" />
                              Lihat Detail
                            </DropdownMenuItem>
                            <DropdownMenuItem @click="openParticipantModal(registration)">
                              <Icon name="user" class="w-4 h-4 mr-2" />
                              Verifikasi Data
                            </DropdownMenuItem>
                            <DropdownMenuItem @click="downloadRegistrationPDF(registration)">
                              <Icon name="download" class="w-4 h-4 mr-2" />
                              Download PDF
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </td>
                  </tr>

                  <!-- Expandable Details Row -->
                  <tr
                    v-if="expandedRows.includes(registration.id_pendaftaran)"
                    class="bg-gray-50 border-b"
                  >
                    <td colspan="7" class="px-6 py-4">
                      <ExpandableRegistrationDetails
                        :registration="registration"
                        @verify="handleInlineVerification"
                        @verify-document="handleDocumentVerification"
                        @verify-participant="handleParticipantVerification"
                        @close="toggleRowExpansion(registration.id_pendaftaran)"
                      />
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          <div class="px-6 py-4 border-t">
            <Pagination
              :links="registrations.links"
              :from="registrations.from"
              :to="registrations.to"
              :total="registrations.total"
            />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Bulk Verify Modal -->
    <BulkVerifyModal
      v-model:show="showBulkVerifyModal"
      :selected-count="selectedRegistrations.length"
      :selected-registrations="getSelectedRegistrationsData()"
      @verify="handleBulkVerify"
      @verify-documents="handleBulkDocumentVerify"
    />

    <!-- Inline Verification Modal -->
    <InlineVerificationModal
      v-model:show="showInlineVerificationModal"
      :registration="currentRegistration"
      @verify="handleInlineVerification"
    />

    <!-- Inline Document Modal -->
    <InlineDocumentModal
      v-model:show="showInlineDocumentModal"
      :registration="currentRegistration"
      @verify="handleDocumentVerification"
    />

    <!-- Inline Participant Modal -->
    <InlineParticipantModal
      v-model:show="showInlineParticipantModal"
      :registration="currentRegistration"
      @verify="handleParticipantVerification"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { Head, router } from '@inertiajs/vue3'
import { ref, computed, watch } from 'vue'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import BulkVerifyModal from './BulkVerifyModal.vue'
import ExpandableRegistrationDetails from './ExpandableRegistrationDetails.vue'
import InlineDocumentModal from './InlineDocumentModal.vue'
import InlineParticipantModal from './InlineParticipantModal.vue'
import InlineVerificationModal from './InlineVerificationModal.vue'
import { type BreadcrumbItem } from '@/types'

// Props
interface Props {
  registrations: {
    data: any[]
    links: any[]
    from: number
    to: number
    total: number
  }
  filters: {
    status?: string
    wilayah?: string
    cabang_lomba?: string
    golongan?: string
    search?: string
  }
  filterOptions: {
    statuses: Array<{ value: string; label: string }>
    wilayah: Array<{ id_wilayah: number; nama_wilayah: string }>
    cabangLomba: Array<{ id_cabang: number; nama_cabang: string }>
    golongan: Array<{ id_golongan: number; nama_golongan: string }>
  }
  stats: {
    total_registrations: number
    pending_verification: number
    verified: number
    approved: number
    rejected: number
  }
}

const props = defineProps<Props>()

// Breadcrumbs
const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard Admin', href: '/admin/dashboard' },
  { title: 'Verifikasi Pendaftaran', href: '/admin/registration-verification' }
]

// Reactive data
const selectedRegistrations = ref<number[]>([])
const expandedRows = ref<number[]>([])
const showBulkVerifyModal = ref(false)
const showInlineDocumentModal = ref(false)
const showInlineParticipantModal = ref(false)
const showInlineVerificationModal = ref(false)
const currentRegistration = ref<any>(null)
const searchForm = ref({
  search: props.filters.search || '',
  status: props.filters.status || '',
  wilayah: props.filters.wilayah || '',
  cabang_lomba: props.filters.cabang_lomba || '',
  golongan: props.filters.golongan || ''
})

// Computed
const allSelected = computed(() => {
  return props.registrations.data.length > 0 &&
         selectedRegistrations.value.length === props.registrations.data.length
})

// Methods
const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedRegistrations.value = props.registrations.data.map(r => r.id_pendaftaran)
  } else {
    selectedRegistrations.value = []
  }
}

const toggleRegistrationSelection = (id: number) => {
  const index = selectedRegistrations.value.indexOf(id)
  if (index > -1) {
    selectedRegistrations.value.splice(index, 1)
  } else {
    selectedRegistrations.value.push(id)
  }
}

const debouncedSearch = debounce(() => {
  applyFilters()
}, 500)

const applyFilters = () => {
  router.get('/admin/registration-verification', searchForm.value, {
    preserveState: true,
    preserveScroll: true
  })
}

const resetFilters = () => {
  searchForm.value = {
    search: '',
    status: '',
    wilayah: '',
    cabang_lomba: '',
    golongan: ''
  }
  applyFilters()
}

const viewRegistration = (registration: any) => {
  router.visit(`/admin/registration-verification/${registration.id_pendaftaran}`)
}

const canVerify = (registration: any) => {
  return ['submitted', 'paid'].includes(registration.status_pendaftaran)
}

const quickVerify = (registration: any, action: 'approve' | 'reject') => {
  const confirmed = confirm(
    `Apakah Anda yakin ingin ${action === 'approve' ? 'menyetujui' : 'menolak'} pendaftaran ini?`
  )

  if (confirmed) {
    router.post(`/admin/registration-verification/${registration.id_pendaftaran}/verify`, {
      action,
      notes: ''
    })
  }
}

// This function is replaced by the enhanced version below

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'draft': 'secondary',
    'submitted': 'default',
    'payment_pending': 'outline',
    'paid': 'default',
    'verified': 'default',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'draft': 'Draft',
    'submitted': 'Submitted',
    'payment_pending': 'Menunggu Pembayaran',
    'paid': 'Dibayar',
    'verified': 'Terverifikasi',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// New methods for enhanced functionality
const toggleRowExpansion = (registrationId: number) => {
  const index = expandedRows.value.indexOf(registrationId)
  if (index > -1) {
    expandedRows.value.splice(index, 1)
  } else {
    expandedRows.value.push(registrationId)
  }
}

const showInlineVerification = (registration: any) => {
  currentRegistration.value = registration
  showInlineVerificationModal.value = true
}

const openDocumentModal = (registration: any) => {
  currentRegistration.value = registration
  showInlineDocumentModal.value = true
}

const openParticipantModal = (registration: any) => {
  currentRegistration.value = registration
  showInlineParticipantModal.value = true
}

const handleInlineVerification = (data: any) => {
  // Optimistic update
  const optimisticUpdate = {
    status_pendaftaran: data.action === 'approve' ? 'verified' : 'rejected',
    verified_by: 'current_user',
    verified_at: new Date().toISOString(),
    catatan_verifikasi: data.notes
  }

  // Apply optimistic update immediately
  updateRegistrationInList(data.registrationId, optimisticUpdate)

  router.post(`/admin/registration-verification/${data.registrationId}/verify`, {
    action: data.action,
    notes: data.notes
  }, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: (page) => {
      // Update with actual server response if needed
      if (page.props.registration) {
        updateRegistrationInList(data.registrationId, page.props.registration)
      }

      // Show success notification
      showNotification('success', `Pendaftaran berhasil ${data.action === 'approve' ? 'disetujui' : 'ditolak'}`)

      // Close expanded row if open
      const expandedIndex = expandedRows.value.indexOf(data.registrationId)
      if (expandedIndex > -1) {
        expandedRows.value.splice(expandedIndex, 1)
      }
    },
    onError: (errors) => {
      // Revert optimistic update on error
      const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === data.registrationId)
      if (registration) {
        // Revert to previous status (this is simplified, in real app you'd store the previous state)
        registration.status_pendaftaran = 'submitted'
        registration.verified_by = null
        registration.verified_at = null
        registration.catatan_verifikasi = null
      }

      showNotification('error', 'Terjadi kesalahan saat memverifikasi pendaftaran')
    }
  })
}

const handleDocumentVerification = (data: any) => {
  // Optimistic update for document status
  updateDocumentStatus(data.registrationId, data.documentId, data.action)

  router.post(`/admin/registration-verification/documents/${data.documentId}/verify`, {
    status_verifikasi: data.action,
    catatan_verifikasi: data.notes
  }, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      showNotification('success', `Dokumen berhasil ${data.action === 'approved' ? 'disetujui' : 'ditolak'}`)
    },
    onError: () => {
      // Revert optimistic update on error
      updateDocumentStatus(data.registrationId, data.documentId, 'pending')
      showNotification('error', 'Terjadi kesalahan saat memverifikasi dokumen')
    }
  })
}

const handleParticipantVerification = (data: any) => {
  router.post(`/admin/registration-verification/${data.registrationId}/verify-participant-data`, data, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      // Update participant verification status
      updateParticipantVerificationStatus(data.registrationId, data.verification_type_id, data.status)
    }
  })
}

const updateRegistrationInList = (registrationId: number, updates: any) => {
  const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === registrationId)
  if (registration) {
    Object.assign(registration, updates)
  }
}

const updateDocumentStatus = (registrationId: number, documentId: number, status: string) => {
  const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === registrationId)
  if (registration && registration.documents) {
    const document = registration.documents.find((d: any) => d.id_dokumen === documentId)
    if (document) {
      document.status_verifikasi = status
      // Recalculate document progress
      recalculateProgress(registration)
    }
  }
}

const updateParticipantVerificationStatus = (registrationId: number, verificationTypeId: number, status: string) => {
  const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === registrationId)
  if (registration && registration.participant_verifications) {
    const verification = registration.participant_verifications.find((v: any) => v.verification_type_id === verificationTypeId)
    if (verification) {
      verification.status = status
      // Recalculate verification progress
      recalculateProgress(registration)
    }
  }
}

const recalculateProgress = (registration: any) => {
  // Recalculate document progress
  if (registration.documents) {
    const totalDocs = registration.documents.length
    const approvedDocs = registration.documents.filter((d: any) => d.status_verifikasi === 'approved').length
    registration.document_status.approved = approvedDocs
    registration.document_status.total = totalDocs
    registration.verification_progress.documents.verified = approvedDocs
    registration.verification_progress.documents.percentage = totalDocs > 0 ? Math.round((approvedDocs / totalDocs) * 100) : 0
  }

  // Recalculate participant verification progress
  if (registration.participant_verifications) {
    const totalVerifications = registration.participant_verifications.length
    const completedVerifications = registration.participant_verifications.filter((v: any) => v.status === 'verified').length
    registration.participant_status.verified = completedVerifications
    registration.verification_progress.verifications.completed = completedVerifications
    registration.verification_progress.verifications.percentage = totalVerifications > 0 ? Math.round((completedVerifications / totalVerifications) * 100) : 0
  }

  // Recalculate overall progress
  const docProgress = registration.verification_progress.documents.percentage || 0
  const verificationProgress = registration.verification_progress.verifications.percentage || 0
  registration.verification_progress.overall_percentage = Math.round((docProgress + verificationProgress) / 2)
}

const downloadRegistrationPDF = (registration: any) => {
  window.open(`/admin/registration-verification/${registration.id_pendaftaran}/download-pdf`, '_blank')
}

// Notification system
const showNotification = (type: 'success' | 'error' | 'info', message: string) => {
  // This would integrate with your notification system
  // For now, we'll use a simple alert or toast
  if (type === 'success') {
    console.log('✅ Success:', message)
  } else if (type === 'error') {
    console.error('❌ Error:', message)
  } else {
    console.info('ℹ️ Info:', message)
  }

  // In a real implementation, you might use a toast library like:
  // toast[type](message)
}

// Enhanced bulk verification with real-time updates
const handleBulkVerify = (data: { action: string; notes: string }) => {
  // Optimistic updates for all selected registrations
  const optimisticStatus = data.action === 'approve' ? 'verified' : 'rejected'
  selectedRegistrations.value.forEach(registrationId => {
    updateRegistrationInList(registrationId, {
      status_pendaftaran: optimisticStatus,
      verified_by: 'current_user',
      verified_at: new Date().toISOString(),
      catatan_verifikasi: data.notes
    })
  })

  router.post('/admin/registration-verification/bulk-verify', {
    registration_ids: selectedRegistrations.value,
    action: data.action,
    notes: data.notes
  }, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      const count = selectedRegistrations.value.length
      const action = data.action === 'approve' ? 'disetujui' : 'ditolak'
      showNotification('success', `${count} pendaftaran berhasil ${action}`)

      selectedRegistrations.value = []
      showBulkVerifyModal.value = false
    },
    onError: () => {
      // Revert optimistic updates on error
      selectedRegistrations.value.forEach(registrationId => {
        const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === registrationId)
        if (registration) {
          registration.status_pendaftaran = 'submitted' // Revert to previous status
          registration.verified_by = null
          registration.verified_at = null
          registration.catatan_verifikasi = null
        }
      })

      showNotification('error', 'Terjadi kesalahan saat memverifikasi pendaftaran secara massal')
    }
  })
}

// Real-time status updates for better UX
const refreshRegistrationData = async () => {
  // This would refresh the current page data without full reload
  router.reload({ only: ['registrations', 'stats'] })
}

// Auto-refresh every 30 seconds to keep data current
const autoRefreshInterval = ref<number | null>(null)

const startAutoRefresh = () => {
  autoRefreshInterval.value = setInterval(() => {
    refreshRegistrationData()
  }, 30000) // 30 seconds
}

const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
    autoRefreshInterval.value = null
  }
}

// Lifecycle hooks for auto-refresh
watch(() => props.show, (newValue) => {
  if (newValue) {
    startAutoRefresh()
  } else {
    stopAutoRefresh()
  }
})

// Cleanup on component unmount
const cleanup = () => {
  stopAutoRefresh()
}

// Make sure to call cleanup when component is unmounted
// This would typically be done in onUnmounted hook

// Helper method to get selected registrations data
const getSelectedRegistrationsData = () => {
  return props.registrations.data.filter((r: any) =>
    selectedRegistrations.value.includes(r.id_pendaftaran)
  )
}

// Handle bulk document verification
const handleBulkDocumentVerify = (data: { action: string; notes: string }) => {
  // Get all documents from selected registrations
  const allDocuments: any[] = []
  selectedRegistrations.value.forEach(registrationId => {
    const registration = props.registrations.data.find((r: any) => r.id_pendaftaran === registrationId)
    if (registration && registration.documents) {
      allDocuments.push(...registration.documents.filter((d: any) => d.status_verifikasi === 'pending'))
    }
  })

  if (allDocuments.length === 0) {
    showNotification('info', 'Tidak ada dokumen yang perlu diverifikasi')
    return
  }

  // Optimistic updates for all documents
  allDocuments.forEach(document => {
    updateDocumentStatus(document.registration_id, document.id_dokumen, data.action === 'approve' ? 'approved' : 'rejected')
  })

  router.post('/admin/registration-verification/documents/bulk-verify', {
    document_ids: allDocuments.map(d => d.id_dokumen),
    action: data.action === 'approve' ? 'approved' : 'rejected',
    notes: data.notes
  }, {
    preserveState: true,
    preserveScroll: true,
    onSuccess: () => {
      const count = allDocuments.length
      const action = data.action === 'approve' ? 'disetujui' : 'ditolak'
      showNotification('success', `${count} dokumen berhasil ${action}`)

      showBulkVerifyModal.value = false
    },
    onError: () => {
      // Revert optimistic updates on error
      allDocuments.forEach(document => {
        updateDocumentStatus(document.registration_id, document.id_dokumen, 'pending')
      })

      showNotification('error', 'Terjadi kesalahan saat memverifikasi dokumen secara massal')
    }
  })
}
</script>
