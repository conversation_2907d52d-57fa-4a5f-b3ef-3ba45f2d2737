<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use App\Models\ParticipantVerification;
use App\Models\VerificationType;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class RegistrationVerificationController extends Controller
{
    /**
     * Check if user has permission to verify registrations
     */
    private function checkVerificationPermission(): void
    {
        $user = Auth::user();
        if (!$user->isAdminProvinsi()) {
            abort(403, 'Unauthorized. Only admin provinsi can verify registrations.');
        }
    }

    /**
     * Display the registration verification dashboard
     */
    public function index(Request $request): Response
    {
        $this->checkVerificationPermission();

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'verifiedBy',
            'approvedBy'
        ]);

        // Apply filters
        $this->applyFilters($query, $request);

        // Apply search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('peserta', function ($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            })->orWhere('nomor_pendaftaran', 'like', "%{$search}%")
              ->orWhere('nomor_peserta', 'like', "%{$search}%");
        }

        // Get paginated results
        $registrations = $query->latest('created_at')->paginate(20)->withQueryString();

        // Add verification progress for each registration
        $registrations->getCollection()->transform(function ($registration) {
            $registration->verification_progress = $this->calculateVerificationProgress($registration);
            $registration->document_status = $this->getDocumentStatus($registration);
            $registration->participant_verification_status = $this->getParticipantVerificationStatus($registration);
            return $registration;
        });

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        return Inertia::render('Admin/RegistrationVerification/Index', [
            'registrations' => $registrations,
            'filters' => $request->only(['status', 'wilayah', 'cabang_lomba', 'golongan', 'search']),
            'filterOptions' => $filterOptions,
            'stats' => $this->getVerificationStats()
        ]);
    }

    /**
     * Show detailed verification view for a specific registration
     */
    public function show(Pendaftaran $pendaftaran): Response
    {
        $this->checkVerificationPermission();

        $pendaftaran->load([
            'peserta.user',
            'peserta.wilayah',
            'peserta.verifications.verificationType',
            'peserta.verifications.verifiedBy',
            'golongan.cabangLomba',
            'golongan.requiredDocumentTypes',
            'verifiedBy',
            'approvedBy'
        ]);

        // Get documents for this registration
        $documents = DokumenPeserta::with(['documentType', 'verifiedBy'])
            ->where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->get();

        // Get required documents for this golongan
        $requiredDocuments = $pendaftaran->golongan->requiredDocumentTypes;

        // Get verification types
        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/RegistrationVerification/Show', [
            'registration' => $pendaftaran,
            'documents' => $documents,
            'requiredDocuments' => $requiredDocuments,
            'verificationTypes' => $verificationTypes,
            'verificationProgress' => $this->calculateVerificationProgress($pendaftaran)
        ]);
    }

    /**
     * Verify a registration (approve or reject)
     */
    public function verify(Request $request, Pendaftaran $pendaftaran)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000'
        ]);

        DB::transaction(function () use ($pendaftaran, $validated) {
            $status = $validated['action'] === 'approve' ? 'verified' : 'rejected';

            $pendaftaran->update([
                'status_pendaftaran' => $status,
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'catatan_verifikasi' => $validated['notes']
            ]);

            // Update peserta status as well
            $pendaftaran->peserta->update([
                'status_peserta' => $status
            ]);
        });

        $message = $validated['action'] === 'approve'
            ? 'Pendaftaran berhasil diverifikasi dan disetujui.'
            : 'Pendaftaran berhasil ditolak.';

        return back()->with('success', $message);
    }

    /**
     * Bulk verify registrations
     */
    public function bulkVerify(Request $request)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'registration_ids' => 'required|array|min:1',
            'registration_ids.*' => 'exists:pendaftaran,id_pendaftaran',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000'
        ]);

        $status = $validated['action'] === 'approve' ? 'verified' : 'rejected';
        $count = 0;

        DB::transaction(function () use ($validated, $status, &$count) {
            $registrations = Pendaftaran::with(['peserta'])
                ->whereIn('id_pendaftaran', $validated['registration_ids'])
                ->get();

            $auditData = [];
            $currentTime = now();
            $userId = Auth::id();

            foreach ($registrations as $registration) {
                // Store audit data before update
                $auditData[] = [
                    'registration_id' => $registration->id_pendaftaran,
                    'previous_status' => $registration->status_pendaftaran,
                    'new_status' => $status,
                    'action_type' => 'bulk_verification',
                    'performed_by' => $userId,
                    'performed_at' => $currentTime,
                    'notes' => $validated['notes'],
                    'participant_name' => $registration->peserta->nama_lengkap,
                    'participant_nik' => $registration->peserta->nik
                ];

                $registration->update([
                    'status_pendaftaran' => $status,
                    'verified_by' => $userId,
                    'verified_at' => $currentTime,
                    'catatan_verifikasi' => $validated['notes']
                ]);

                // Update peserta status as well
                $registration->peserta->update([
                    'status_peserta' => $status
                ]);

                $count++;
            }

            // Log bulk verification audit trail
            Log::info('Bulk verification performed', [
                'user_id' => $userId,
                'action' => $validated['action'],
                'count' => $count,
                'registrations' => $auditData
            ]);
        });

        $action = $validated['action'] === 'approve' ? 'disetujui' : 'ditolak';
        return back()->with('success', "{$count} pendaftaran berhasil {$action}.");
    }

    /**
     * Download a document
     */
    public function downloadDocument(DokumenPeserta $dokumen)
    {
        $this->checkVerificationPermission();

        $filePath = storage_path('app/public/' . $dokumen->path_file);

        if (!file_exists($filePath)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download($filePath, $dokumen->nama_file);
    }

    /**
     * Bulk verify registrations by status
     */
    public function bulkVerifyByStatus(Request $request)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'status_filter' => 'required|in:submitted,paid',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000',
            'wilayah_filter' => 'nullable|exists:wilayah,id_wilayah',
            'golongan_filter' => 'nullable|exists:golongan,id_golongan'
        ]);

        $query = Pendaftaran::where('status_pendaftaran', $validated['status_filter']);

        // Apply additional filters
        if (!empty($validated['wilayah_filter'])) {
            $query->whereHas('peserta', function ($q) use ($validated) {
                $q->where('id_wilayah', $validated['wilayah_filter']);
            });
        }

        if (!empty($validated['golongan_filter'])) {
            $query->where('id_golongan', $validated['golongan_filter']);
        }

        $registrationIds = $query->pluck('id_pendaftaran')->toArray();

        if (empty($registrationIds)) {
            return back()->with('error', 'Tidak ada pendaftaran yang sesuai dengan kriteria filter.');
        }

        // Use existing bulk verify method
        $request->merge(['registration_ids' => $registrationIds]);
        return $this->bulkVerify($request);
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, Request $request): void
    {
        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_pendaftaran', $request->status);
        }

        // Filter by wilayah
        if ($request->filled('wilayah')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('id_wilayah', $request->wilayah);
            });
        }

        // Filter by cabang lomba
        if ($request->filled('cabang_lomba')) {
            $query->whereHas('golongan', function ($q) use ($request) {
                $q->where('id_cabang', $request->cabang_lomba);
            });
        }

        // Filter by golongan
        if ($request->filled('golongan')) {
            $query->where('id_golongan', $request->golongan);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by verification status
        if ($request->filled('verification_status')) {
            switch ($request->verification_status) {
                case 'pending_verification':
                    $query->whereIn('status_pendaftaran', ['submitted', 'paid']);
                    break;
                case 'verified':
                    $query->where('status_pendaftaran', 'verified');
                    break;
                case 'approved':
                    $query->where('status_pendaftaran', 'approved');
                    break;
                case 'rejected':
                    $query->where('status_pendaftaran', 'rejected');
                    break;
                case 'incomplete_documents':
                    $query->whereHas('peserta', function ($q) {
                        $q->whereHas('pendaftaran', function ($subQ) {
                            $subQ->whereDoesntHave('dokumenPeserta', function ($docQ) {
                                $docQ->where('status_verifikasi', 'approved');
                            });
                        });
                    });
                    break;
            }
        }

        // Filter by registration type
        if ($request->filled('registration_type')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('registration_type', $request->registration_type);
            });
        }

        // Filter by gender
        if ($request->filled('gender')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('jenis_kelamin', $request->gender);
            });
        }
    }

    /**
     * Build advanced query with optimized joins and subqueries
     */
    private function buildAdvancedQuery(Request $request)
    {
        $query = Pendaftaran::query()
            ->select([
                'pendaftaran.*',
                'peserta.nama_lengkap',
                'peserta.nik',
                'peserta.jenis_kelamin',
                'peserta.registration_type',
                'wilayah.nama_wilayah',
                'golongan.nama_golongan',
                'cabang_lomba.nama_cabang'
            ])
            ->join('peserta', 'pendaftaran.id_peserta', '=', 'peserta.id_peserta')
            ->join('wilayah', 'peserta.id_wilayah', '=', 'wilayah.id_wilayah')
            ->join('golongan', 'pendaftaran.id_golongan', '=', 'golongan.id_golongan')
            ->join('cabang_lomba', 'golongan.id_cabang', '=', 'cabang_lomba.id_cabang')
            ->with([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba',
                'verifiedBy',
                'approvedBy'
            ]);

        // Add document count subqueries for better performance
        $query->withCount([
            'dokumenPeserta as total_documents',
            'dokumenPeserta as approved_documents' => function ($q) {
                $q->where('status_verifikasi', 'approved');
            },
            'dokumenPeserta as pending_documents' => function ($q) {
                $q->where('status_verifikasi', 'pending');
            },
            'dokumenPeserta as rejected_documents' => function ($q) {
                $q->where('status_verifikasi', 'rejected');
            }
        ]);

        // Add participant verification count subqueries
        $query->withCount([
            'peserta.verifications as total_verifications',
            'peserta.verifications as completed_verifications' => function ($q) {
                $q->where('status', 'verified');
            }
        ]);

        return $query;
    }

    /**
     * Apply search with full-text capabilities
     */
    private function applyAdvancedSearch($query, string $search): void
    {
        $query->where(function ($q) use ($search) {
            $q->where('peserta.nama_lengkap', 'like', "%{$search}%")
              ->orWhere('peserta.nik', 'like', "%{$search}%")
              ->orWhere('pendaftaran.nomor_pendaftaran', 'like', "%{$search}%")
              ->orWhere('pendaftaran.nomor_peserta', 'like', "%{$search}%")
              ->orWhere('wilayah.nama_wilayah', 'like', "%{$search}%")
              ->orWhere('golongan.nama_golongan', 'like', "%{$search}%")
              ->orWhere('cabang_lomba.nama_cabang', 'like', "%{$search}%");
        });
    }

    /**
     * Get filter options for the dashboard
     */
    private function getFilterOptions(): array
    {
        return [
            'statuses' => [
                ['value' => 'draft', 'label' => 'Draft'],
                ['value' => 'submitted', 'label' => 'Submitted'],
                ['value' => 'payment_pending', 'label' => 'Payment Pending'],
                ['value' => 'paid', 'label' => 'Paid'],
                ['value' => 'verified', 'label' => 'Verified'],
                ['value' => 'approved', 'label' => 'Approved'],
                ['value' => 'rejected', 'label' => 'Rejected']
            ],
            'wilayah' => Wilayah::aktif()->orderBy('nama_wilayah')->get(['id_wilayah', 'nama_wilayah']),
            'cabangLomba' => CabangLomba::aktif()->orderBy('nama_cabang')->get(['id_cabang', 'nama_cabang']),
            'golongan' => Golongan::with('cabangLomba')->aktif()->orderBy('nama_golongan')->get()
        ];
    }

    /**
     * Get verification statistics
     */
    private function getVerificationStats(): array
    {
        return [
            'total_registrations' => Pendaftaran::count(),
            'pending_verification' => Pendaftaran::whereIn('status_pendaftaran', ['submitted', 'paid'])->count(),
            'verified' => Pendaftaran::where('status_pendaftaran', 'verified')->count(),
            'approved' => Pendaftaran::where('status_pendaftaran', 'approved')->count(),
            'rejected' => Pendaftaran::where('status_pendaftaran', 'rejected')->count(),
            'documents_pending' => DokumenPeserta::where('status_verifikasi', 'pending')->count(),
            'documents_approved' => DokumenPeserta::where('status_verifikasi', 'approved')->count(),
            'documents_rejected' => DokumenPeserta::where('status_verifikasi', 'rejected')->count()
        ];
    }

    /**
     * Calculate verification progress for a registration
     */
    private function calculateVerificationProgress(Pendaftaran $pendaftaran): array
    {
        // Get required documents for this golongan
        $requiredDocuments = $pendaftaran->golongan->requiredDocumentTypes()->count();
        $submittedDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)->count();
        $verifiedDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('status_verifikasi', 'approved')->count();

        // Get participant verifications
        $requiredVerifications = VerificationType::where('is_required', true)->count();
        $completedVerifications = ParticipantVerification::where('id_peserta', $pendaftaran->id_peserta)
            ->where('status', 'verified')->count();

        return [
            'documents' => [
                'required' => $requiredDocuments,
                'submitted' => $submittedDocuments,
                'verified' => $verifiedDocuments,
                'percentage' => $requiredDocuments > 0 ? round(($verifiedDocuments / $requiredDocuments) * 100) : 0
            ],
            'verifications' => [
                'required' => $requiredVerifications,
                'completed' => $completedVerifications,
                'percentage' => $requiredVerifications > 0 ? round(($completedVerifications / $requiredVerifications) * 100) : 0
            ],
            'overall_percentage' => $this->calculateOverallProgress($requiredDocuments, $verifiedDocuments, $requiredVerifications, $completedVerifications)
        ];
    }

    /**
     * Calculate overall verification progress percentage
     */
    private function calculateOverallProgress(int $requiredDocs, int $verifiedDocs, int $requiredVerifs, int $completedVerifs): int
    {
        $totalRequired = $requiredDocs + $requiredVerifs;
        $totalCompleted = $verifiedDocs + $completedVerifs;

        return $totalRequired > 0 ? round(($totalCompleted / $totalRequired) * 100) : 0;
    }

    /**
     * Get document status for a registration
     */
    private function getDocumentStatus(Pendaftaran $pendaftaran): array
    {
        $documents = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->selectRaw('status_verifikasi, COUNT(*) as count')
            ->groupBy('status_verifikasi')
            ->pluck('count', 'status_verifikasi')
            ->toArray();

        return [
            'pending' => $documents['pending'] ?? 0,
            'approved' => $documents['approved'] ?? 0,
            'rejected' => $documents['rejected'] ?? 0,
            'total' => array_sum($documents)
        ];
    }

    /**
     * Get participant verification status
     */
    private function getParticipantVerificationStatus(Pendaftaran $pendaftaran): array
    {
        $verifications = ParticipantVerification::where('id_peserta', $pendaftaran->id_peserta)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'pending' => $verifications['pending'] ?? 0,
            'verified' => $verifications['verified'] ?? 0,
            'rejected' => $verifications['rejected'] ?? 0,
            'total' => array_sum($verifications)
        ];
    }

    /**
     * Verify a document (approve or reject)
     */
    public function verifyDocument(Request $request, DokumenPeserta $dokumen)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $dokumen->update([
            'status_verifikasi' => $validated['status_verifikasi'],
            'catatan_verifikasi' => $validated['catatan_verifikasi'],
            'verified_by' => Auth::id(),
            'verified_at' => now()
        ]);

        $message = $validated['status_verifikasi'] === 'approved'
            ? 'Dokumen berhasil disetujui.'
            : 'Dokumen berhasil ditolak.';

        return back()->with('success', $message);
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerifyDocuments(Request $request)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'document_ids' => 'required|array|min:1',
            'document_ids.*' => 'exists:dokumen_peserta,id_dokumen',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500'
        ]);

        $status = $validated['action'] === 'approve' ? 'approved' : 'rejected';
        $count = 0;

        DB::transaction(function () use ($validated, $status, &$count) {
            $documents = DokumenPeserta::whereIn('id_dokumen', $validated['document_ids'])->get();

            foreach ($documents as $document) {
                $document->update([
                    'status_verifikasi' => $status,
                    'catatan_verifikasi' => $validated['notes'],
                    'verified_by' => Auth::id(),
                    'verified_at' => now()
                ]);
                $count++;
            }
        });

        $action = $validated['action'] === 'approve' ? 'disetujui' : 'ditolak';
        return back()->with('success', "{$count} dokumen berhasil {$action}.");
    }

    /**
     * Verify participant data (NIK, personal info, etc.)
     */
    public function verifyParticipantData(Request $request, Pendaftaran $pendaftaran)
    {
        $this->checkVerificationPermission();

        $validated = $request->validate([
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000',
            'verification_data' => 'nullable|array'
        ]);

        // Create or update participant verification
        ParticipantVerification::updateOrCreate(
            [
                'id_peserta' => $pendaftaran->id_peserta,
                'verification_type_id' => $validated['verification_type_id']
            ],
            [
                'status' => $validated['status'],
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'notes' => $validated['notes'],
                'verification_data' => $validated['verification_data']
            ]
        );

        $message = $validated['status'] === 'verified'
            ? 'Data peserta berhasil diverifikasi.'
            : 'Data peserta ditolak.';

        return back()->with('success', $message);
    }
}
