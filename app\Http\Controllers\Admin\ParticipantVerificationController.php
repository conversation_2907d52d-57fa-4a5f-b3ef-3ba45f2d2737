<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Peserta;
use App\Models\VerificationType;
use App\Models\ParticipantVerification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class ParticipantVerificationController extends Controller
{
    /**
     * Check if current user can manage participant verifications
     */
    private function checkPermission()
    {
        if (!in_array(Auth::user()->role, ['admin', 'superadmin'])) {
            abort(403, 'Akses ditolak. Hanya admin dan superadmin yang dapat mengelola verifikasi peserta.');
        }
    }

    /**
     * Display participant verification dashboard
     */
    public function index(Request $request): Response
    {
        $this->checkPermission();

        $query = Peserta::with(['verifications.verificationType', 'pendaftaran.golongan.cabangLomba']);

        // Filter by verification status
        if ($request->filled('verification_status')) {
            $status = $request->verification_status;
            if ($status === 'complete') {
                $query->whereHas('verifications', function($q) {
                    $q->where('status', 'verified');
                });
            } elseif ($status === 'incomplete') {
                $query->whereDoesntHave('verifications', function($q) {
                    $q->where('status', 'verified');
                });
            }
        }

        // Filter by verification type
        if ($request->filled('verification_type')) {
            $query->whereHas('verifications.verificationType', function($q) use ($request) {
                $q->where('code', $request->verification_type);
            });
        }

        // Search by participant name or NIK
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_peserta', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $participants = $query->paginate(20);
        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/ParticipantVerifications/Index', [
            'participants' => $participants,
            'verificationTypes' => $verificationTypes,
            'filters' => $request->only(['verification_status', 'verification_type', 'search'])
        ]);
    }

    /**
     * Show participant verification details
     */
    public function show(Peserta $peserta): Response
    {
        $this->checkPermission();

        $peserta->load([
            'verifications.verificationType',
            'verifications.verifiedBy',
            'pendaftaran.golongan.cabangLomba'
        ]);

        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/ParticipantVerifications/Show', [
            'peserta' => $peserta,
            'verificationTypes' => $verificationTypes
        ]);
    }

    /**
     * Verify participant for specific verification type
     */
    public function verify(Request $request, Peserta $peserta)
    {
        $this->checkPermission();

        $validated = $request->validate([
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000',
            'verification_data' => 'nullable|array'
        ]);

        // Create or update verification record
        ParticipantVerification::updateOrCreate(
            [
                'id_peserta' => $peserta->id_peserta,
                'verification_type_id' => $validated['verification_type_id']
            ],
            [
                'status' => $validated['status'],
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'notes' => $validated['notes'],
                'verification_data' => $validated['verification_data'] ?? []
            ]
        );

        $verificationType = VerificationType::find($validated['verification_type_id']);
        $statusText = $validated['status'] === 'verified' ? 'diverifikasi' : 'ditolak';

        return back()->with('success', "Verifikasi {$verificationType->name} berhasil {$statusText}.");
    }

    /**
     * Bulk verify participants
     */
    public function bulkVerify(Request $request)
    {
        $this->checkPermission();

        $validated = $request->validate([
            'participant_ids' => 'required|array',
            'participant_ids.*' => 'exists:peserta,id_peserta',
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000'
        ]);

        $count = 0;
        foreach ($validated['participant_ids'] as $participantId) {
            ParticipantVerification::updateOrCreate(
                [
                    'id_peserta' => $participantId,
                    'verification_type_id' => $validated['verification_type_id']
                ],
                [
                    'status' => $validated['status'],
                    'verified_by' => Auth::id(),
                    'verified_at' => now(),
                    'notes' => $validated['notes']
                ]
            );
            $count++;
        }

        $verificationType = VerificationType::find($validated['verification_type_id']);
        $statusText = $validated['status'] === 'verified' ? 'diverifikasi' : 'ditolak';

        return back()->with('success', "{$count} peserta berhasil {$statusText} untuk {$verificationType->name}.");
    }

    /**
     * NIK verification interface
     */
    public function nikVerification(Request $request): Response
    {
        $this->checkPermission();

        $nikVerificationType = VerificationType::where('code', 'nik')->first();

        if (!$nikVerificationType) {
            abort(404, 'Jenis verifikasi NIK tidak ditemukan.');
        }

        $query = Peserta::with(['pendaftaran.golongan.cabangLomba']);

        // Get participants without NIK verification or with pending NIK verification
        $query->where(function($q) use ($nikVerificationType) {
            $q->whereDoesntHave('verifications', function($subQ) use ($nikVerificationType) {
                $subQ->where('verification_type_id', $nikVerificationType->id_verification_type);
            })->orWhereHas('verifications', function($subQ) use ($nikVerificationType) {
                $subQ->where('verification_type_id', $nikVerificationType->id_verification_type)
                     ->where('status', 'pending');
            });
        });

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('nama_peserta', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            });
        }

        $participants = $query->paginate(20);

        return Inertia::render('Admin/ParticipantVerifications/NikVerification', [
            'participants' => $participants,
            'nikVerificationType' => $nikVerificationType,
            'filters' => $request->only(['search'])
        ]);
    }
}
