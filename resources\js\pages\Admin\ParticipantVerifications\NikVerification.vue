<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Verifikasi NIK Peserta" />
    <Heading title="Verifikasi NIK Peserta" />

    <div class="space-y-6">
      <!-- Search and Filters -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama peserta, NIK..."
                @input="search"
              />
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Statistics -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="users" class="w-8 h-8 text-blue-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Total Peserta</p>
                <p class="text-2xl font-bold text-gray-900">{{ participants.total }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="clock" class="w-8 h-8 text-yellow-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Menunggu Verifikasi</p>
                <p class="text-2xl font-bold text-gray-900">{{ participants.data.length }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="check-circle" class="w-8 h-8 text-green-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Terverifikasi</p>
                <p class="text-2xl font-bold text-gray-900">{{ verifiedCount }}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <Icon name="x-circle" class="w-8 h-8 text-red-600" />
              </div>
              <div class="ml-4">
                <p class="text-sm font-medium text-gray-500">Ditolak</p>
                <p class="text-2xl font-bold text-gray-900">{{ rejectedCount }}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Bulk Actions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-600">
              {{ selectedParticipants.length }} peserta dipilih
            </div>
            <div class="flex items-center space-x-2">
              <Button
                @click="bulkVerify('verified')"
                :disabled="selectedParticipants.length === 0"
                variant="outline"
                class="text-green-600 hover:text-green-700"
              >
                <Icon name="check" class="w-4 h-4 mr-2" />
                Verifikasi Terpilih
              </Button>
              <Button
                @click="bulkVerify('rejected')"
                :disabled="selectedParticipants.length === 0"
                variant="outline"
                class="text-red-600 hover:text-red-700"
              >
                <Icon name="x" class="w-4 h-4 mr-2" />
                Tolak Terpilih
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Participants List -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left">
                    <Checkbox
                      :checked="allSelected"
                      @update:checked="toggleSelectAll"
                    />
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Peserta
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    NIK
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status NIK
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="participant in participants.data" :key="participant.id_peserta" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Checkbox
                      :checked="selectedParticipants.includes(participant.id_peserta)"
                      @update:checked="toggleParticipantSelection(participant.id_peserta)"
                    />
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.nama_peserta }}</div>
                      <div class="text-sm text-gray-500">{{ participant.tempat_lahir }}, {{ formatDate(participant.tanggal_lahir) }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-mono text-gray-900">{{ participant.nik }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ participant.pendaftaran?.[0]?.golongan?.nama_golongan }}</div>
                      <div class="text-sm text-gray-500">{{ participant.pendaftaran?.[0]?.golongan?.cabang_lomba?.nama_cabang }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge variant="warning">Menunggu Verifikasi</Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex items-center space-x-2">
                      <Button
                        @click="verifyNik(participant, 'verified')"
                        size="sm"
                        class="bg-green-600 hover:bg-green-700"
                      >
                        <Icon name="check" class="w-4 h-4 mr-1" />
                        Valid
                      </Button>
                      <Button
                        @click="verifyNik(participant, 'rejected')"
                        size="sm"
                        variant="destructive"
                      >
                        <Icon name="x" class="w-4 h-4 mr-1" />
                        Invalid
                      </Button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-700">
          Menampilkan {{ participants.from }} sampai {{ participants.to }} dari {{ participants.total }} hasil
        </div>
        <Pagination
          :current-page="participants.current_page"
          :last-page="participants.last_page"
          :links="participants.links"
        />
      </div>
    </div>

    <!-- Verification Notes Dialog -->
    <Dialog v-model:open="notesDialog.open">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Catatan Verifikasi</DialogTitle>
          <DialogDescription>
            Tambahkan catatan untuk verifikasi NIK {{ notesDialog.participant?.nama_peserta }}
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div>
            <Label for="notes">Catatan</Label>
            <Textarea
              id="notes"
              v-model="notesDialog.notes"
              placeholder="Tambahkan catatan verifikasi..."
              rows="3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="notesDialog.open = false">Batal</Button>
          <Button @click="submitVerification">Simpan</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </AppLayout>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { Head, router, useForm } from '@inertiajs/vue3'
import { debounce } from 'lodash'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import Pagination from '@/components/Pagination.vue'
import Icon from '@/components/Icon.vue'

const props = defineProps({
  participants: Object,
  nikVerificationType: Object,
  filters: Object
})

const breadcrumbItems = [
  { label: 'Dashboard', href: route('admin.dashboard') },
  { label: 'Verifikasi Peserta', href: route('admin.participant-verifications.index') },
  { label: 'Verifikasi NIK', href: null }
]

const filters = reactive({
  search: props.filters?.search || ''
})

const selectedParticipants = ref([])
const verifiedCount = ref(0) // This should come from backend
const rejectedCount = ref(0) // This should come from backend

const notesDialog = reactive({
  open: false,
  participant: null,
  status: null,
  notes: ''
})

const allSelected = computed(() => {
  return props.participants.data.length > 0 && selectedParticipants.value.length === props.participants.data.length
})

const search = debounce(() => {
  router.get(route('admin.participant-verifications.nik-verification'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  search()
}

const toggleSelectAll = (checked) => {
  if (checked) {
    selectedParticipants.value = props.participants.data.map(p => p.id_peserta)
  } else {
    selectedParticipants.value = []
  }
}

const toggleParticipantSelection = (participantId) => {
  const index = selectedParticipants.value.indexOf(participantId)
  if (index > -1) {
    selectedParticipants.value.splice(index, 1)
  } else {
    selectedParticipants.value.push(participantId)
  }
}

const verifyNik = (participant, status) => {
  notesDialog.participant = participant
  notesDialog.status = status
  notesDialog.notes = ''
  notesDialog.open = true
}

const submitVerification = () => {
  router.post(route('admin.participant-verifications.verify', notesDialog.participant.id_peserta), {
    verification_type_id: props.nikVerificationType.id_verification_type,
    status: notesDialog.status,
    notes: notesDialog.notes
  }, {
    onSuccess: () => {
      notesDialog.open = false
    }
  })
}

const bulkVerify = (status) => {
  router.post(route('admin.participant-verifications.bulk-verify'), {
    participant_ids: selectedParticipants.value,
    verification_type_id: props.nikVerificationType.id_verification_type,
    status: status,
    notes: `Bulk ${status === 'verified' ? 'verification' : 'rejection'} NIK`
  }, {
    onSuccess: () => {
      selectedParticipants.value = []
    }
  })
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('id-ID')
}
</script>
