<template>
  <div class="border rounded-lg p-4 space-y-3">
    <div class="flex items-center justify-between">
      <div>
        <h4 class="font-medium">{{ documentType.name }}</h4>
        <p class="text-sm text-gray-500">{{ documentType.description }}</p>
      </div>
      <Badge :variant="getDocumentStatusVariant()">
        {{ getDocumentStatusLabel() }}
      </Badge>
    </div>

    <!-- Document List -->
    <div v-if="documents.length > 0" class="space-y-2">
      <div 
        v-for="document in documents" 
        :key="document.id_dokumen"
        class="flex items-center justify-between p-3 bg-gray-50 rounded border"
      >
        <div class="flex items-center space-x-3">
          <Icon name="file" class="w-5 h-5 text-gray-400" />
          <div>
            <p class="text-sm font-medium">{{ document.nama_file }}</p>
            <p class="text-xs text-gray-500">
              {{ formatFileSize(document.ukuran_file) }} • 
              {{ formatDate(document.created_at) }}
            </p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <Badge :variant="getStatusVariant(document.status_verifikasi)">
            {{ getStatusLabel(document.status_verifikasi) }}
          </Badge>
          
          <div v-if="document.status_verifikasi === 'pending'" class="flex space-x-1">
            <Button
              @click="verifyDocument(document.id_dokumen, 'approved')"
              size="sm"
              class="bg-green-600 hover:bg-green-700 text-white"
            >
              <Icon name="check" class="w-3 h-3" />
            </Button>
            <Button
              @click="verifyDocument(document.id_dokumen, 'rejected')"
              size="sm"
              variant="destructive"
            >
              <Icon name="x" class="w-3 h-3" />
            </Button>
          </div>
          
          <Button
            @click="downloadDocument(document)"
            size="sm"
            variant="outline"
          >
            <Icon name="download" class="w-3 h-3" />
          </Button>
        </div>
      </div>
    </div>

    <!-- No Documents -->
    <div v-else class="text-center py-4 text-gray-500">
      <Icon name="fileX" class="w-8 h-8 mx-auto mb-2 text-gray-300" />
      <p class="text-sm">Belum ada dokumen yang diunggah</p>
    </div>

    <!-- Verification Notes -->
    <div v-if="hasVerificationNotes" class="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
      <h5 class="text-sm font-medium text-blue-800 mb-1">Catatan Verifikasi:</h5>
      <div v-for="document in documentsWithNotes" :key="document.id_dokumen" class="text-sm text-blue-700">
        <strong>{{ document.nama_file }}:</strong> {{ document.catatan_verifikasi }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  documentType: any
  documents: any[]
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'verify': [data: { documentId: number; action: string; notes?: string }]
}>()

// Computed
const hasVerificationNotes = computed(() => {
  return props.documents.some(doc => doc.catatan_verifikasi)
})

const documentsWithNotes = computed(() => {
  return props.documents.filter(doc => doc.catatan_verifikasi)
})

// Methods
const verifyDocument = (documentId: number, action: string) => {
  const notes = action === 'rejected' ? prompt('Masukkan alasan penolakan:') : ''
  if (action === 'rejected' && !notes) return
  
  emit('verify', { documentId, action, notes })
}

const downloadDocument = (document: any) => {
  // Implementation for document download
  window.open(`/admin/registration-verification/documents/${document.id_dokumen}/download`, '_blank')
}

const getDocumentStatusVariant = () => {
  if (props.documents.length === 0) return 'secondary'
  
  const allApproved = props.documents.every(doc => doc.status_verifikasi === 'approved')
  const hasRejected = props.documents.some(doc => doc.status_verifikasi === 'rejected')
  const hasPending = props.documents.some(doc => doc.status_verifikasi === 'pending')
  
  if (hasRejected) return 'destructive'
  if (allApproved) return 'default'
  if (hasPending) return 'outline'
  return 'secondary'
}

const getDocumentStatusLabel = () => {
  if (props.documents.length === 0) return 'Belum Diunggah'
  
  const allApproved = props.documents.every(doc => doc.status_verifikasi === 'approved')
  const hasRejected = props.documents.some(doc => doc.status_verifikasi === 'rejected')
  const hasPending = props.documents.some(doc => doc.status_verifikasi === 'pending')
  
  if (hasRejected) return 'Ditolak'
  if (allApproved) return 'Disetujui'
  if (hasPending) return 'Menunggu Verifikasi'
  return 'Tidak Lengkap'
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}
</script>
