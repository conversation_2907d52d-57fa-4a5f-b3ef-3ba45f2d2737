<template>
  <div class="space-y-6 p-4 bg-white rounded-lg border">
    <!-- Quick Actions Header -->
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold text-gray-900">Detail Verifikasi</h3>
      <div class="flex items-center space-x-2">
        <Button @click="$emit('close')" size="sm" variant="ghost">
          <Icon name="x" class="w-4 h-4" />
        </Button>
      </div>
    </div>

    <!-- Progress Overview -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card class="border-l-4 border-l-blue-500">
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Progress Keseluruhan</p>
              <p class="text-2xl font-bold text-blue-600">{{ registration.verification_progress.overall_percentage }}%</p>
            </div>
            <Icon name="target" class="w-8 h-8 text-blue-500" />
          </div>
        </CardContent>
      </Card>

      <Card class="border-l-4 border-l-green-500">
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Dokumen</p>
              <p class="text-2xl font-bold text-green-600">
                {{ registration.document_status.approved }}/{{ registration.document_status.total }}
              </p>
            </div>
            <Icon name="file" class="w-8 h-8 text-green-500" />
          </div>
        </CardContent>
      </Card>

      <Card class="border-l-4 border-l-purple-500">
        <CardContent class="p-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-600">Data Peserta</p>
              <p class="text-2xl font-bold text-purple-600">
                {{ registration.participant_status.verified }}/{{ registration.participant_status.total }}
              </p>
            </div>
            <Icon name="user" class="w-8 h-8 text-purple-500" />
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Tabbed Interface -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          class="py-2 px-1 border-b-2 font-medium text-sm transition-colors"
          :class="{
            'border-blue-500 text-blue-600': activeTab === tab.id,
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== tab.id
          }"
        >
          <Icon :name="tab.icon" class="w-4 h-4 mr-2 inline" />
          {{ tab.label }}
          <Badge v-if="tab.count !== undefined" class="ml-2" variant="secondary">
            {{ tab.count }}
          </Badge>
        </button>
      </nav>
    </div>

    <!-- Tab Content -->
    <div class="mt-6">
      <!-- Quick Verification Tab -->
      <div v-if="activeTab === 'verification'" class="space-y-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="font-medium mb-3">Verifikasi Cepat</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button
              @click="showQuickApprove = true"
              class="bg-green-600 hover:bg-green-700 text-white justify-center"
              :disabled="!canVerify"
            >
              <Icon name="check" class="w-4 h-4 mr-2" />
              Setujui Pendaftaran
            </Button>
            <Button
              @click="showQuickReject = true"
              variant="destructive"
              class="justify-center"
              :disabled="!canVerify"
            >
              <Icon name="x" class="w-4 h-4 mr-2" />
              Tolak Pendaftaran
            </Button>
          </div>
        </div>

        <!-- Quick Approve Form -->
        <div v-if="showQuickApprove" class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h5 class="font-medium text-green-800 mb-3">Setujui Pendaftaran</h5>
          <form @submit.prevent="handleQuickVerification('approve')" class="space-y-3">
            <div>
              <Label for="approve-notes">Catatan Persetujuan (Opsional)</Label>
              <Textarea
                id="approve-notes"
                v-model="quickVerificationForm.notes"
                placeholder="Tambahkan catatan persetujuan..."
                rows="2"
              />
            </div>
            <div class="flex items-center space-x-2">
              <Button type="submit" class="bg-green-600 hover:bg-green-700">
                Konfirmasi Setujui
              </Button>
              <Button type="button" @click="showQuickApprove = false" variant="outline">
                Batal
              </Button>
            </div>
          </form>
        </div>

        <!-- Quick Reject Form -->
        <div v-if="showQuickReject" class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h5 class="font-medium text-red-800 mb-3">Tolak Pendaftaran</h5>
          <form @submit.prevent="handleQuickVerification('reject')" class="space-y-3">
            <div>
              <Label for="reject-notes">Alasan Penolakan (Wajib)</Label>
              <Textarea
                id="reject-notes"
                v-model="quickVerificationForm.notes"
                placeholder="Jelaskan alasan penolakan..."
                rows="3"
                required
              />
            </div>
            <div class="flex items-center space-x-2">
              <Button type="submit" variant="destructive">
                Konfirmasi Tolak
              </Button>
              <Button type="button" @click="showQuickReject = false" variant="outline">
                Batal
              </Button>
            </div>
          </form>
        </div>
      </div>

      <!-- Documents Tab -->
      <div v-if="activeTab === 'documents'" class="space-y-4">
        <div v-if="registration.documents && registration.documents.length > 0">
          <div
            v-for="document in registration.documents"
            :key="document.id_dokumen"
            class="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1">
                <div class="flex items-center space-x-3">
                  <Icon name="file" class="w-5 h-5 text-gray-400" />
                  <div>
                    <p class="font-medium">{{ document.nama_file }}</p>
                    <p class="text-sm text-gray-500">{{ document.document_type?.name }}</p>
                  </div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <Badge :variant="getDocumentStatusVariant(document.status_verifikasi)">
                  {{ getDocumentStatusLabel(document.status_verifikasi) }}
                </Badge>
                <Button
                  @click="verifyDocument(document, 'approved')"
                  size="sm"
                  class="bg-green-600 hover:bg-green-700 text-white"
                  :disabled="document.status_verifikasi === 'approved'"
                >
                  <Icon name="check" class="w-3 h-3" />
                </Button>
                <Button
                  @click="verifyDocument(document, 'rejected')"
                  size="sm"
                  variant="destructive"
                  :disabled="document.status_verifikasi === 'rejected'"
                >
                  <Icon name="x" class="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          <Icon name="fileX" class="w-12 h-12 mx-auto mb-2 text-gray-300" />
          <p>Belum ada dokumen yang diunggah</p>
        </div>
      </div>

      <!-- Participant Data Tab -->
      <div v-if="activeTab === 'participant'" class="space-y-6">
        <!-- Participant Information Cards -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Personal Information -->
          <Card class="border-l-4 border-l-blue-500">
            <CardContent class="p-4">
              <div class="flex items-center justify-between mb-3">
                <h5 class="font-medium">Informasi Personal</h5>
                <Button
                  @click="togglePersonalDataEdit"
                  size="sm"
                  variant="outline"
                  class="text-blue-600 border-blue-600 hover:bg-blue-50"
                >
                  <Icon name="edit" class="w-3 h-3 mr-1" />
                  {{ showPersonalDataEdit ? 'Batal' : 'Verifikasi' }}
                </Button>
              </div>

              <div class="space-y-3 text-sm">
                <!-- Name Verification -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Nama Lengkap:</span>
                    <p class="font-medium">{{ registration.peserta.nama_lengkap }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('nama_lengkap')">
                      {{ getFieldVerificationLabel('nama_lengkap') }}
                    </Badge>
                    <Button
                      v-if="!isFieldVerified('nama_lengkap')"
                      @click="verifyField('nama_lengkap', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- NIK Verification -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">NIK:</span>
                    <p class="font-medium font-mono">{{ registration.peserta.nik }}</p>
                    <div v-if="nikValidationResult" class="mt-1">
                      <Badge :variant="nikValidationResult.valid ? 'default' : 'destructive'" class="text-xs">
                        {{ nikValidationResult.valid ? 'NIK Valid' : 'NIK Tidak Valid' }}
                      </Badge>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('nik')">
                      {{ getFieldVerificationLabel('nik') }}
                    </Badge>
                    <Button
                      @click="validateNIK"
                      size="sm"
                      variant="outline"
                      class="p-1"
                      :disabled="isValidatingNIK"
                    >
                      <Icon :name="isValidatingNIK ? 'loader' : 'search'" class="w-3 h-3" :class="{ 'animate-spin': isValidatingNIK }" />
                    </Button>
                    <Button
                      v-if="!isFieldVerified('nik')"
                      @click="verifyField('nik', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- Birth Information -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Tempat, Tanggal Lahir:</span>
                    <p class="font-medium">{{ registration.peserta.tempat_lahir }}, {{ formatDate(registration.peserta.tanggal_lahir) }}</p>
                    <div v-if="ageCalculation" class="mt-1">
                      <span class="text-xs text-gray-500">Usia: {{ ageCalculation.years }} tahun {{ ageCalculation.months }} bulan</span>
                    </div>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('birth_info')">
                      {{ getFieldVerificationLabel('birth_info') }}
                    </Badge>
                    <Button
                      v-if="!isFieldVerified('birth_info')"
                      @click="verifyField('birth_info', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- Gender -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Jenis Kelamin:</span>
                    <p class="font-medium">{{ registration.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('jenis_kelamin')">
                      {{ getFieldVerificationLabel('jenis_kelamin') }}
                    </Badge>
                    <Button
                      v-if="!isFieldVerified('jenis_kelamin')"
                      @click="verifyField('jenis_kelamin', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <!-- Contact & Location Information -->
          <Card class="border-l-4 border-l-green-500">
            <CardContent class="p-4">
              <div class="flex items-center justify-between mb-3">
                <h5 class="font-medium">Kontak & Lokasi</h5>
                <Button
                  @click="toggleContactDataEdit"
                  size="sm"
                  variant="outline"
                  class="text-green-600 border-green-600 hover:bg-green-50"
                >
                  <Icon name="edit" class="w-3 h-3 mr-1" />
                  {{ showContactDataEdit ? 'Batal' : 'Verifikasi' }}
                </Button>
              </div>

              <div class="space-y-3 text-sm">
                <!-- Wilayah -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Wilayah:</span>
                    <p class="font-medium">{{ registration.peserta.wilayah?.nama_wilayah }}</p>
                    <p class="text-xs text-gray-500">{{ registration.peserta.wilayah?.level_wilayah }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('wilayah')">
                      {{ getFieldVerificationLabel('wilayah') }}
                    </Badge>
                    <Button
                      v-if="!isFieldVerified('wilayah')"
                      @click="verifyField('wilayah', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- Phone -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">No. Telepon:</span>
                    <p class="font-medium">{{ registration.peserta.no_telepon }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('no_telepon')">
                      {{ getFieldVerificationLabel('no_telepon') }}
                    </Badge>
                    <Button
                      @click="validatePhone"
                      size="sm"
                      variant="outline"
                      class="p-1"
                    >
                      <Icon name="phone" class="w-3 h-3" />
                    </Button>
                    <Button
                      v-if="!isFieldVerified('no_telepon')"
                      @click="verifyField('no_telepon', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- Email -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Email:</span>
                    <p class="font-medium">{{ registration.peserta.email }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('email')">
                      {{ getFieldVerificationLabel('email') }}
                    </Badge>
                    <Button
                      @click="validateEmail"
                      size="sm"
                      variant="outline"
                      class="p-1"
                    >
                      <Icon name="mail" class="w-3 h-3" />
                    </Button>
                    <Button
                      v-if="!isFieldVerified('email')"
                      @click="verifyField('email', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                <!-- Institution -->
                <div class="flex items-center justify-between p-2 rounded hover:bg-gray-50">
                  <div class="flex-1">
                    <span class="text-gray-600">Instansi:</span>
                    <p class="font-medium">{{ registration.peserta.instansi_asal || '-' }}</p>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge :variant="getFieldVerificationStatus('instansi_asal')">
                      {{ getFieldVerificationLabel('instansi_asal') }}
                    </Badge>
                    <Button
                      v-if="!isFieldVerified('instansi_asal')"
                      @click="verifyField('instansi_asal', true)"
                      size="sm"
                      class="bg-green-600 hover:bg-green-700 text-white p-1"
                    >
                      <Icon name="check" class="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Eligibility Verification -->
        <Card class="border-l-4 border-l-purple-500">
          <CardContent class="p-4">
            <div class="flex items-center justify-between mb-3">
              <h5 class="font-medium">Verifikasi Kelayakan</h5>
              <Badge :variant="getEligibilityStatusVariant()">
                {{ getEligibilityStatusLabel() }}
              </Badge>
            </div>

            <div class="space-y-3">
              <!-- Age Eligibility -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <Icon name="calendar" class="w-4 h-4 text-purple-600" />
                    <span class="font-medium">Kelayakan Usia</span>
                  </div>
                  <p class="text-sm text-gray-600 mt-1">
                    Usia: {{ ageCalculation?.years || 0 }} tahun untuk kategori {{ registration.golongan?.nama_golongan }}
                  </p>
                  <div v-if="ageEligibility" class="mt-2">
                    <Badge :variant="ageEligibility.eligible ? 'default' : 'destructive'" class="text-xs">
                      {{ ageEligibility.eligible ? 'Memenuhi Syarat' : 'Tidak Memenuhi Syarat' }}
                    </Badge>
                    <p v-if="!ageEligibility.eligible" class="text-xs text-red-600 mt-1">
                      {{ ageEligibility.reason }}
                    </p>
                  </div>
                </div>
                <Button
                  @click="checkAgeEligibility"
                  size="sm"
                  variant="outline"
                  class="text-purple-600 border-purple-600 hover:bg-purple-50"
                >
                  <Icon name="calculator" class="w-3 h-3 mr-1" />
                  Cek
                </Button>
              </div>

              <!-- Gender Eligibility -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <Icon name="users" class="w-4 h-4 text-purple-600" />
                    <span class="font-medium">Kelayakan Jenis Kelamin</span>
                  </div>
                  <p class="text-sm text-gray-600 mt-1">
                    {{ registration.peserta.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }} untuk kategori {{ registration.golongan?.nama_golongan }}
                  </p>
                  <div v-if="genderEligibility" class="mt-2">
                    <Badge :variant="genderEligibility.eligible ? 'default' : 'destructive'" class="text-xs">
                      {{ genderEligibility.eligible ? 'Memenuhi Syarat' : 'Tidak Memenuhi Syarat' }}
                    </Badge>
                  </div>
                </div>
                <Button
                  @click="checkGenderEligibility"
                  size="sm"
                  variant="outline"
                  class="text-purple-600 border-purple-600 hover:bg-purple-50"
                >
                  <Icon name="checkCircle" class="w-3 h-3 mr-1" />
                  Cek
                </Button>
              </div>

              <!-- Regional Eligibility -->
              <div class="flex items-center justify-between p-3 bg-gray-50 rounded">
                <div class="flex-1">
                  <div class="flex items-center space-x-2">
                    <Icon name="mapPin" class="w-4 h-4 text-purple-600" />
                    <span class="font-medium">Kelayakan Wilayah</span>
                  </div>
                  <p class="text-sm text-gray-600 mt-1">
                    {{ registration.peserta.wilayah?.nama_wilayah }} ({{ registration.peserta.wilayah?.level_wilayah }})
                  </p>
                  <div v-if="regionalEligibility" class="mt-2">
                    <Badge :variant="regionalEligibility.eligible ? 'default' : 'destructive'" class="text-xs">
                      {{ regionalEligibility.eligible ? 'Memenuhi Syarat' : 'Tidak Memenuhi Syarat' }}
                    </Badge>
                  </div>
                </div>
                <Button
                  @click="checkRegionalEligibility"
                  size="sm"
                  variant="outline"
                  class="text-purple-600 border-purple-600 hover:bg-purple-50"
                >
                  <Icon name="map" class="w-3 h-3 mr-1" />
                  Cek
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Competition-Specific Verification -->
        <Card class="border-l-4 border-l-orange-500">
          <CardContent class="p-4">
            <div class="flex items-center justify-between mb-3">
              <h5 class="font-medium">Verifikasi Khusus Lomba</h5>
              <Button
                @click="toggleCompetitionVerification"
                size="sm"
                variant="outline"
                class="text-orange-600 border-orange-600 hover:bg-orange-50"
              >
                <Icon name="trophy" class="w-3 h-3 mr-1" />
                {{ showCompetitionVerification ? 'Tutup' : 'Buka' }}
              </Button>
            </div>

            <div v-if="showCompetitionVerification" class="space-y-3">
              <!-- Previous Participation Check -->
              <div class="p-3 bg-orange-50 rounded border border-orange-200">
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium text-orange-800">Riwayat Partisipasi</span>
                  <Button
                    @click="checkPreviousParticipation"
                    size="sm"
                    variant="outline"
                    :disabled="isCheckingHistory"
                  >
                    <Icon :name="isCheckingHistory ? 'loader' : 'search'" class="w-3 h-3 mr-1" :class="{ 'animate-spin': isCheckingHistory }" />
                    Cek Riwayat
                  </Button>
                </div>
                <div v-if="participationHistory" class="text-sm text-orange-700">
                  <p v-if="participationHistory.hasParticipated">
                    <strong>Pernah berpartisipasi:</strong> {{ participationHistory.lastParticipation.year }}
                    ({{ participationHistory.lastParticipation.category }})
                  </p>
                  <p v-else>Belum pernah berpartisipasi dalam MTQ sebelumnya</p>
                </div>
              </div>

              <!-- Duplicate Registration Check -->
              <div class="p-3 bg-orange-50 rounded border border-orange-200">
                <div class="flex items-center justify-between mb-2">
                  <span class="font-medium text-orange-800">Cek Duplikasi</span>
                  <Button
                    @click="checkDuplicateRegistration"
                    size="sm"
                    variant="outline"
                    :disabled="isCheckingDuplicate"
                  >
                    <Icon :name="isCheckingDuplicate ? 'loader' : 'copy'" class="w-3 h-3 mr-1" :class="{ 'animate-spin': isCheckingDuplicate }" />
                    Cek Duplikasi
                  </Button>
                </div>
                <div v-if="duplicateCheck" class="text-sm text-orange-700">
                  <Badge :variant="duplicateCheck.hasDuplicate ? 'destructive' : 'default'" class="text-xs">
                    {{ duplicateCheck.hasDuplicate ? 'Ditemukan Duplikasi' : 'Tidak Ada Duplikasi' }}
                  </Badge>
                  <div v-if="duplicateCheck.hasDuplicate && duplicateCheck.duplicates" class="mt-2 space-y-1">
                    <p class="font-medium">Pendaftaran Duplikat:</p>
                    <div v-for="duplicate in duplicateCheck.duplicates" :key="duplicate.id" class="text-xs">
                      • {{ duplicate.nomor_pendaftaran }} - {{ duplicate.golongan }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Advanced Participant Verification Panel -->
        <div v-if="showAdvancedVerification" class="mt-6">
          <ParticipantDataVerificationPanel
            :participant="registration.peserta"
            :registration="registration"
            @approve-participant="handleParticipantApproval"
            @reject-participant="handleParticipantRejection"
            @update-verification="handleVerificationUpdate"
          />
        </div>

        <!-- Quick Actions -->
        <div class="flex items-center space-x-2 pt-4 border-t">
          <Button
            @click="verifyAllParticipantData"
            class="bg-blue-600 hover:bg-blue-700 text-white"
            :disabled="!hasUnverifiedParticipantData"
          >
            <Icon name="checkCircle" class="w-4 h-4 mr-2" />
            Verifikasi Semua Data
          </Button>
          <Button
            @click="toggleAdvancedVerification"
            variant="outline"
            class="border-purple-600 text-purple-600 hover:bg-purple-50"
          >
            <Icon name="settings" class="w-4 h-4 mr-2" />
            {{ showAdvancedVerification ? 'Tutup' : 'Verifikasi Lanjutan' }}
          </Button>
          <Button
            @click="openParticipantModal"
            variant="outline"
          >
            <Icon name="user" class="w-4 h-4 mr-2" />
            Verifikasi Detail
          </Button>
        </div>

        <!-- Verification Summary -->
        <div v-if="showVerificationSummary" class="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
          <div class="flex items-center justify-between mb-3">
            <h5 class="font-medium text-blue-800">Ringkasan Verifikasi Peserta</h5>
            <Badge :variant="getParticipantVerificationVariant()" class="text-sm">
              {{ getParticipantVerificationLabel() }}
            </Badge>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-600">{{ verifiedFieldsCount }}</div>
              <div class="text-blue-700">Field Terverifikasi</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-600">{{ eligibilityPassedCount }}</div>
              <div class="text-blue-700">Kelayakan Lulus</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-600">{{ validationPassedCount }}</div>
              <div class="text-blue-700">Validasi Lulus</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-indigo-600">{{ Math.round(participantVerificationProgress) }}%</div>
              <div class="text-blue-700">Progress Total</div>
            </div>
          </div>

          <div class="mt-3">
            <div class="w-full bg-blue-200 rounded-full h-2">
              <div
                class="bg-blue-600 h-2 rounded-full transition-all duration-500"
                :style="{ width: `${participantVerificationProgress}%` }"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import Icon from '@/components/Icon.vue'
import ParticipantDataVerificationPanel from './ParticipantDataVerificationPanel.vue'

// Props
interface Props {
  registration: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'verify': [data: { registrationId: number; action: string; notes: string }]
  'verify-document': [data: { registrationId: number; documentId: number; action: string; notes?: string }]
  'verify-participant': [data: { registrationId: number; verification_type_id: number; status: string; notes?: string }]
  'close': []
}>()

// Reactive data
const activeTab = ref('verification')
const showQuickApprove = ref(false)
const showQuickReject = ref(false)
const showPersonalDataEdit = ref(false)
const showContactDataEdit = ref(false)
const showCompetitionVerification = ref(false)
const showAdvancedVerification = ref(false)
const showVerificationSummary = ref(true)
const isValidatingNIK = ref(false)
const isCheckingHistory = ref(false)
const isCheckingDuplicate = ref(false)

const quickVerificationForm = ref({
  notes: ''
})

// Field verification tracking
const fieldVerifications = ref<Record<string, { verified: boolean; notes?: string }>>({
  nama_lengkap: { verified: false },
  nik: { verified: false },
  birth_info: { verified: false },
  jenis_kelamin: { verified: false },
  wilayah: { verified: false },
  no_telepon: { verified: false },
  email: { verified: false },
  instansi_asal: { verified: false }
})

// Validation results
const nikValidationResult = ref<{ valid: boolean; message?: string } | null>(null)
const ageCalculation = ref<{ years: number; months: number; days: number } | null>(null)
const ageEligibility = ref<{ eligible: boolean; reason?: string } | null>(null)
const genderEligibility = ref<{ eligible: boolean; reason?: string } | null>(null)
const regionalEligibility = ref<{ eligible: boolean; reason?: string } | null>(null)
const participationHistory = ref<{ hasParticipated: boolean; lastParticipation?: any } | null>(null)
const duplicateCheck = ref<{ hasDuplicate: boolean; duplicates?: any[] } | null>(null)

// Computed
const canVerify = computed(() => {
  return ['submitted', 'paid'].includes(props.registration.status_pendaftaran)
})

const tabs = computed(() => [
  {
    id: 'verification',
    label: 'Verifikasi',
    icon: 'check',
    count: undefined
  },
  {
    id: 'documents',
    label: 'Dokumen',
    icon: 'file',
    count: props.registration.documents?.length || 0
  },
  {
    id: 'participant',
    label: 'Data Peserta',
    icon: 'user',
    count: props.registration.participant_verifications?.length || 0
  }
])

const hasUnverifiedParticipantData = computed(() => {
  return props.registration.participant_verifications?.some((v: any) => v.status === 'pending') || false
})

// Calculate age automatically when component loads
const calculateAge = () => {
  if (!props.registration.peserta.tanggal_lahir) return null

  const birthDate = new Date(props.registration.peserta.tanggal_lahir)
  const today = new Date()

  let years = today.getFullYear() - birthDate.getFullYear()
  let months = today.getMonth() - birthDate.getMonth()
  let days = today.getDate() - birthDate.getDate()

  if (days < 0) {
    months--
    days += new Date(today.getFullYear(), today.getMonth(), 0).getDate()
  }

  if (months < 0) {
    years--
    months += 12
  }

  return { years, months, days }
}

// Initialize age calculation
if (props.registration.peserta.tanggal_lahir) {
  ageCalculation.value = calculateAge()
}

// Methods
const handleQuickVerification = (action: string) => {
  emit('verify', {
    registrationId: props.registration.id_pendaftaran,
    action,
    notes: quickVerificationForm.value.notes
  })

  // Reset form
  quickVerificationForm.value.notes = ''
  showQuickApprove.value = false
  showQuickReject.value = false
}

const verifyDocument = (document: any, action: string) => {
  const notes = action === 'rejected' ? prompt('Alasan penolakan:') : ''
  if (action === 'rejected' && !notes) return

  emit('verify-document', {
    registrationId: props.registration.id_pendaftaran,
    documentId: document.id_dokumen,
    action,
    notes
  })
}

const verifyParticipantData = (verification: any, status: string) => {
  emit('verify-participant', {
    registrationId: props.registration.id_pendaftaran,
    verification_type_id: verification.verification_type_id,
    status,
    notes: ''
  })
}

const getDocumentStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'approved': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getDocumentStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'approved': 'Disetujui',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const getVerificationStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    'pending': 'outline',
    'verified': 'default',
    'rejected': 'destructive'
  }
  return variants[status] || 'secondary'
}

const getVerificationStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'pending': 'Pending',
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak'
  }
  return labels[status] || status
}

const verifyAllParticipantData = () => {
  // Verify all pending participant verifications
  props.registration.participant_verifications?.forEach((verification: any) => {
    if (verification.status === 'pending') {
      verifyParticipantData(verification, 'verified')
    }
  })
}

const showParticipantDetails = (verification: any) => {
  // This could open a detailed modal or expand inline details
  console.log('Show participant details for:', verification)
}

const openParticipantModal = () => {
  // Emit event to open the participant modal
  emit('verify-participant', {
    registrationId: props.registration.id_pendaftaran,
    verification_type_id: 0, // Special case for opening modal
    status: 'modal',
    notes: ''
  })
}

const formatDate = (dateString: string) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Field verification methods
const isFieldVerified = (fieldName: string) => {
  return fieldVerifications.value[fieldName]?.verified || false
}

const getFieldVerificationStatus = (fieldName: string) => {
  return isFieldVerified(fieldName) ? 'default' : 'outline'
}

const getFieldVerificationLabel = (fieldName: string) => {
  return isFieldVerified(fieldName) ? 'Terverifikasi' : 'Pending'
}

const verifyField = (fieldName: string, verified: boolean, notes?: string) => {
  fieldVerifications.value[fieldName] = { verified, notes }

  // Emit verification event
  emit('verify-participant', {
    registrationId: props.registration.id_pendaftaran,
    verification_type_id: 0, // Field-level verification
    status: verified ? 'verified' : 'rejected',
    notes: notes || `Field ${fieldName} ${verified ? 'verified' : 'rejected'}`,
    field_name: fieldName
  })
}

// Toggle methods
const togglePersonalDataEdit = () => {
  showPersonalDataEdit.value = !showPersonalDataEdit.value
}

const toggleContactDataEdit = () => {
  showContactDataEdit.value = !showContactDataEdit.value
}

const toggleCompetitionVerification = () => {
  showCompetitionVerification.value = !showCompetitionVerification.value
}

// NIK validation
const validateNIK = async () => {
  isValidatingNIK.value = true

  try {
    // Simulate NIK validation API call
    await new Promise(resolve => setTimeout(resolve, 1500))

    const nik = props.registration.peserta.nik

    // Basic NIK validation (16 digits)
    const isValidLength = nik && nik.length === 16
    const isNumeric = /^\d+$/.test(nik)

    // Extract birth date from NIK (positions 7-12: DDMMYY)
    if (isValidLength && isNumeric) {
      const day = parseInt(nik.substring(6, 8))
      const month = parseInt(nik.substring(8, 10))
      const year = parseInt(nik.substring(10, 12))

      // Adjust for gender (female NIK has day + 40)
      const actualDay = day > 40 ? day - 40 : day
      const actualGender = day > 40 ? 'P' : 'L'

      // Check if gender matches
      const genderMatches = actualGender === props.registration.peserta.jenis_kelamin

      nikValidationResult.value = {
        valid: isValidLength && isNumeric && genderMatches,
        message: genderMatches ? 'NIK valid dan sesuai dengan jenis kelamin' : 'NIK tidak sesuai dengan jenis kelamin'
      }
    } else {
      nikValidationResult.value = {
        valid: false,
        message: 'Format NIK tidak valid (harus 16 digit)'
      }
    }

    // Auto-verify if valid
    if (nikValidationResult.value.valid) {
      verifyField('nik', true, 'NIK tervalidasi otomatis')
    }
  } catch (error) {
    nikValidationResult.value = {
      valid: false,
      message: 'Gagal memvalidasi NIK'
    }
  } finally {
    isValidatingNIK.value = false
  }
}

// Phone validation
const validatePhone = () => {
  const phone = props.registration.peserta.no_telepon
  const phoneRegex = /^(\+62|62|0)[0-9]{9,12}$/

  if (phoneRegex.test(phone)) {
    verifyField('no_telepon', true, 'Format nomor telepon valid')
  } else {
    alert('Format nomor telepon tidak valid')
  }
}

// Email validation
const validateEmail = () => {
  const email = props.registration.peserta.email
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  if (emailRegex.test(email)) {
    verifyField('email', true, 'Format email valid')
  } else {
    alert('Format email tidak valid')
  }
}

// Eligibility checking methods
const checkAgeEligibility = () => {
  if (!ageCalculation.value) {
    ageEligibility.value = { eligible: false, reason: 'Tanggal lahir tidak valid' }
    return
  }

  const age = ageCalculation.value.years
  const category = props.registration.golongan?.nama_golongan?.toLowerCase() || ''

  // Define age requirements for different categories
  const ageRequirements: Record<string, { min: number; max: number }> = {
    'anak-anak': { min: 7, max: 12 },
    'remaja': { min: 13, max: 17 },
    'dewasa': { min: 18, max: 40 },
    'senior': { min: 41, max: 100 }
  }

  // Find matching category
  let requirement = null
  for (const [key, req] of Object.entries(ageRequirements)) {
    if (category.includes(key)) {
      requirement = req
      break
    }
  }

  if (!requirement) {
    ageEligibility.value = { eligible: true, reason: 'Kategori tidak memiliki batasan usia khusus' }
    return
  }

  const eligible = age >= requirement.min && age <= requirement.max
  ageEligibility.value = {
    eligible,
    reason: eligible
      ? `Usia ${age} tahun memenuhi syarat untuk kategori ${category} (${requirement.min}-${requirement.max} tahun)`
      : `Usia ${age} tahun tidak memenuhi syarat untuk kategori ${category} (${requirement.min}-${requirement.max} tahun)`
  }
}

const checkGenderEligibility = () => {
  const gender = props.registration.peserta.jenis_kelamin
  const category = props.registration.golongan?.nama_golongan?.toLowerCase() || ''

  // Check if category has gender restrictions
  const isMaleOnly = category.includes('putra') || category.includes('laki')
  const isFemaleOnly = category.includes('putri') || category.includes('perempuan')

  let eligible = true
  let reason = 'Tidak ada batasan jenis kelamin untuk kategori ini'

  if (isMaleOnly && gender !== 'L') {
    eligible = false
    reason = 'Kategori ini khusus untuk peserta laki-laki'
  } else if (isFemaleOnly && gender !== 'P') {
    eligible = false
    reason = 'Kategori ini khusus untuk peserta perempuan'
  } else if (isMaleOnly && gender === 'L') {
    reason = 'Jenis kelamin sesuai dengan kategori putra'
  } else if (isFemaleOnly && gender === 'P') {
    reason = 'Jenis kelamin sesuai dengan kategori putri'
  }

  genderEligibility.value = { eligible, reason }
}

const checkRegionalEligibility = () => {
  const wilayah = props.registration.peserta.wilayah

  if (!wilayah) {
    regionalEligibility.value = { eligible: false, reason: 'Data wilayah tidak lengkap' }
    return
  }

  // Check if wilayah is active and valid
  const eligible = wilayah.status === 'aktif'
  regionalEligibility.value = {
    eligible,
    reason: eligible
      ? `Wilayah ${wilayah.nama_wilayah} aktif dan valid`
      : `Wilayah ${wilayah.nama_wilayah} tidak aktif atau tidak valid`
  }
}

const getEligibilityStatusVariant = () => {
  const checks = [ageEligibility.value, genderEligibility.value, regionalEligibility.value]
  const hasResults = checks.some(check => check !== null)

  if (!hasResults) return 'outline'

  const allEligible = checks.every(check => check?.eligible !== false)
  const hasIneligible = checks.some(check => check?.eligible === false)

  if (hasIneligible) return 'destructive'
  if (allEligible) return 'default'
  return 'outline'
}

const getEligibilityStatusLabel = () => {
  const checks = [ageEligibility.value, genderEligibility.value, regionalEligibility.value]
  const hasResults = checks.some(check => check !== null)

  if (!hasResults) return 'Belum Dicek'

  const allEligible = checks.every(check => check?.eligible !== false)
  const hasIneligible = checks.some(check => check?.eligible === false)

  if (hasIneligible) return 'Tidak Memenuhi Syarat'
  if (allEligible) return 'Memenuhi Syarat'
  return 'Sebagian Dicek'
}

// Competition-specific verification methods
const checkPreviousParticipation = async () => {
  isCheckingHistory.value = true

  try {
    // Simulate API call to check participation history
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Mock data - in real implementation, this would come from API
    const hasParticipated = Math.random() > 0.7 // 30% chance of previous participation

    if (hasParticipated) {
      participationHistory.value = {
        hasParticipated: true,
        lastParticipation: {
          year: '2022',
          category: 'Dewasa Putra',
          achievement: 'Juara 3'
        }
      }
    } else {
      participationHistory.value = {
        hasParticipated: false
      }
    }
  } catch (error) {
    console.error('Error checking participation history:', error)
  } finally {
    isCheckingHistory.value = false
  }
}

const checkDuplicateRegistration = async () => {
  isCheckingDuplicate.value = true

  try {
    // Simulate API call to check for duplicate registrations
    await new Promise(resolve => setTimeout(resolve, 1500))

    // Mock data - in real implementation, this would search by NIK, name, etc.
    const hasDuplicate = Math.random() > 0.8 // 20% chance of duplicate

    if (hasDuplicate) {
      duplicateCheck.value = {
        hasDuplicate: true,
        duplicates: [
          {
            id: 'REG-2024-001',
            nomor_pendaftaran: 'MTQ-2024-001',
            golongan: 'Dewasa Putra'
          }
        ]
      }
    } else {
      duplicateCheck.value = {
        hasDuplicate: false
      }
    }
  } catch (error) {
    console.error('Error checking duplicate registration:', error)
  } finally {
    isCheckingDuplicate.value = false
  }
}

// Auto-run some checks when component loads
const runInitialChecks = () => {
  // Auto-calculate age
  if (props.registration.peserta.tanggal_lahir) {
    ageCalculation.value = calculateAge()
  }

  // Auto-check basic eligibility
  setTimeout(() => {
    checkAgeEligibility()
    checkGenderEligibility()
    checkRegionalEligibility()
  }, 500)
}

// Run initial checks
runInitialChecks()

// Computed properties for verification summary
const verifiedFieldsCount = computed(() => {
  return Object.values(fieldVerifications.value).filter((field: any) => field.verified).length
})

const eligibilityPassedCount = computed(() => {
  let count = 0
  if (ageEligibility.value?.eligible) count++
  if (genderEligibility.value?.eligible) count++
  if (regionalEligibility.value?.eligible) count++
  return count
})

const validationPassedCount = computed(() => {
  let count = 0
  if (nikValidationResult.value?.valid) count++
  // Add other validation counts as needed
  return count
})

const participantVerificationProgress = computed(() => {
  const totalFields = Object.keys(fieldVerifications.value).length
  const totalEligibilityChecks = 3
  const totalValidations = 1 // NIK validation for now

  const totalItems = totalFields + totalEligibilityChecks + totalValidations
  const completedItems = verifiedFieldsCount.value + eligibilityPassedCount.value + validationPassedCount.value

  return totalItems > 0 ? (completedItems / totalItems) * 100 : 0
})

// Methods for enhanced verification
const toggleAdvancedVerification = () => {
  showAdvancedVerification.value = !showAdvancedVerification.value
}

const getParticipantVerificationVariant = () => {
  const progress = participantVerificationProgress.value
  if (progress === 100) return 'default'
  if (progress >= 75) return 'secondary'
  if (progress >= 50) return 'outline'
  return 'destructive'
}

const getParticipantVerificationLabel = () => {
  const progress = participantVerificationProgress.value
  if (progress === 100) return 'Lengkap'
  if (progress >= 75) return 'Hampir Selesai'
  if (progress >= 50) return 'Dalam Progress'
  if (progress >= 25) return 'Baru Dimulai'
  return 'Belum Dimulai'
}

const handleParticipantApproval = () => {
  // Emit approval event
  emit('verify-participant', {
    registrationId: props.registration.id_pendaftaran,
    verification_type_id: 999, // Special ID for full participant approval
    status: 'verified',
    notes: 'Semua data peserta telah diverifikasi dan disetujui'
  })

  // Mark all fields as verified
  Object.keys(fieldVerifications.value).forEach(field => {
    fieldVerifications.value[field].verified = true
  })

  showAdvancedVerification.value = false
}

const handleParticipantRejection = () => {
  const reason = prompt('Alasan penolakan data peserta:')
  if (!reason) return

  emit('verify-participant', {
    registrationId: props.registration.id_pendaftaran,
    verification_type_id: 999, // Special ID for full participant rejection
    status: 'rejected',
    notes: reason
  })
}

const handleVerificationUpdate = (data: any) => {
  // Handle updates from the advanced verification panel
  console.log('Verification update:', data)
}
</script>
