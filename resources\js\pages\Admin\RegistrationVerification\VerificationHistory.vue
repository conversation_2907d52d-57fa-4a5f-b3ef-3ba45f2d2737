<template>
  <div class="space-y-4">
    <!-- Registration Verification History -->
    <div v-if="registration.verified_at || registration.approved_at" class="space-y-3">
      <h4 class="font-medium text-gray-900">Riwayat Verifikasi Pendaftaran</h4>
      
      <div class="space-y-2">
        <!-- Verification Event -->
        <div v-if="registration.verified_at" class="flex items-start space-x-3 p-3 bg-green-50 rounded border border-green-200">
          <div class="flex-shrink-0 mt-1">
            <Icon name="checkCircle" class="w-5 h-5 text-green-600" />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium text-green-800">
                Pendaftaran {{ registration.status_pendaftaran === 'verified' ? 'Diverifikasi' : 'Disetujui' }}
              </p>
              <span class="text-xs text-green-600">
                {{ formatDate(registration.verified_at) }}
              </span>
            </div>
            <p class="text-sm text-green-700 mt-1">
              Oleh: {{ registration.verified_by?.nama_lengkap || 'System' }}
            </p>
            <div v-if="registration.catatan_verifikasi" class="mt-2 p-2 bg-green-100 rounded text-sm text-green-800">
              <strong>Catatan:</strong> {{ registration.catatan_verifikasi }}
            </div>
          </div>
        </div>

        <!-- Approval Event -->
        <div v-if="registration.approved_at && registration.approved_at !== registration.verified_at" class="flex items-start space-x-3 p-3 bg-blue-50 rounded border border-blue-200">
          <div class="flex-shrink-0 mt-1">
            <Icon name="checkCircle" class="w-5 h-5 text-blue-600" />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium text-blue-800">Pendaftaran Disetujui</p>
              <span class="text-xs text-blue-600">
                {{ formatDate(registration.approved_at) }}
              </span>
            </div>
            <p class="text-sm text-blue-700 mt-1">
              Oleh: {{ registration.approved_by?.nama_lengkap || 'System' }}
            </p>
            <div v-if="registration.catatan_approval" class="mt-2 p-2 bg-blue-100 rounded text-sm text-blue-800">
              <strong>Catatan:</strong> {{ registration.catatan_approval }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Participant Verification History -->
    <div v-if="participantVerifications.length > 0" class="space-y-3">
      <h4 class="font-medium text-gray-900">Riwayat Verifikasi Data Peserta</h4>
      
      <div class="space-y-2">
        <div 
          v-for="verification in participantVerifications" 
          :key="verification.id_verification"
          class="flex items-start space-x-3 p-3 rounded border"
          :class="{
            'bg-green-50 border-green-200': verification.status === 'verified',
            'bg-red-50 border-red-200': verification.status === 'rejected',
            'bg-yellow-50 border-yellow-200': verification.status === 'pending'
          }"
        >
          <div class="flex-shrink-0 mt-1">
            <Icon 
              :name="getVerificationIcon(verification.status)" 
              class="w-5 h-5"
              :class="{
                'text-green-600': verification.status === 'verified',
                'text-red-600': verification.status === 'rejected',
                'text-yellow-600': verification.status === 'pending'
              }"
            />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium"
                :class="{
                  'text-green-800': verification.status === 'verified',
                  'text-red-800': verification.status === 'rejected',
                  'text-yellow-800': verification.status === 'pending'
                }"
              >
                {{ verification.verification_type.name }} - {{ getStatusLabel(verification.status) }}
              </p>
              <span class="text-xs"
                :class="{
                  'text-green-600': verification.status === 'verified',
                  'text-red-600': verification.status === 'rejected',
                  'text-yellow-600': verification.status === 'pending'
                }"
              >
                {{ verification.verified_at ? formatDate(verification.verified_at) : 'Pending' }}
              </span>
            </div>
            <p v-if="verification.verified_by" class="text-sm mt-1"
              :class="{
                'text-green-700': verification.status === 'verified',
                'text-red-700': verification.status === 'rejected',
                'text-yellow-700': verification.status === 'pending'
              }"
            >
              Oleh: {{ verification.verified_by.nama_lengkap }}
            </p>
            <div v-if="verification.notes" class="mt-2 p-2 rounded text-sm"
              :class="{
                'bg-green-100 text-green-800': verification.status === 'verified',
                'bg-red-100 text-red-800': verification.status === 'rejected',
                'bg-yellow-100 text-yellow-800': verification.status === 'pending'
              }"
            >
              <strong>Catatan:</strong> {{ verification.notes }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Document Verification History -->
    <div v-if="documentVerifications.length > 0" class="space-y-3">
      <h4 class="font-medium text-gray-900">Riwayat Verifikasi Dokumen</h4>
      
      <div class="space-y-2">
        <div 
          v-for="document in documentVerifications" 
          :key="document.id_dokumen"
          class="flex items-start space-x-3 p-3 rounded border"
          :class="{
            'bg-green-50 border-green-200': document.status_verifikasi === 'approved',
            'bg-red-50 border-red-200': document.status_verifikasi === 'rejected',
            'bg-yellow-50 border-yellow-200': document.status_verifikasi === 'pending'
          }"
        >
          <div class="flex-shrink-0 mt-1">
            <Icon name="file" class="w-5 h-5 text-gray-400" />
          </div>
          <div class="flex-1">
            <div class="flex items-center justify-between">
              <p class="text-sm font-medium"
                :class="{
                  'text-green-800': document.status_verifikasi === 'approved',
                  'text-red-800': document.status_verifikasi === 'rejected',
                  'text-yellow-800': document.status_verifikasi === 'pending'
                }"
              >
                {{ document.nama_file }} - {{ getDocumentStatusLabel(document.status_verifikasi) }}
              </p>
              <span class="text-xs"
                :class="{
                  'text-green-600': document.status_verifikasi === 'approved',
                  'text-red-600': document.status_verifikasi === 'rejected',
                  'text-yellow-600': document.status_verifikasi === 'pending'
                }"
              >
                {{ document.verified_at ? formatDate(document.verified_at) : 'Pending' }}
              </span>
            </div>
            <p v-if="document.verified_by" class="text-sm mt-1"
              :class="{
                'text-green-700': document.status_verifikasi === 'approved',
                'text-red-700': document.status_verifikasi === 'rejected',
                'text-yellow-700': document.status_verifikasi === 'pending'
              }"
            >
              Oleh: {{ document.verified_by.nama_lengkap }}
            </p>
            <div v-if="document.catatan_verifikasi" class="mt-2 p-2 rounded text-sm"
              :class="{
                'bg-green-100 text-green-800': document.status_verifikasi === 'approved',
                'bg-red-100 text-red-800': document.status_verifikasi === 'rejected',
                'bg-yellow-100 text-yellow-800': document.status_verifikasi === 'pending'
              }"
            >
              <strong>Catatan:</strong> {{ document.catatan_verifikasi }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No History -->
    <div v-if="!hasAnyHistory" class="text-center py-8 text-gray-500">
      <Icon name="clock" class="w-8 h-8 mx-auto mb-2 text-gray-300" />
      <p class="text-sm">Belum ada riwayat verifikasi</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  registration: any
}

const props = defineProps<Props>()

// Computed
const participantVerifications = computed(() => {
  return props.registration.peserta?.verifications?.filter((v: any) => v.verified_at) || []
})

const documentVerifications = computed(() => {
  // This would come from the documents prop in the parent component
  // For now, we'll assume it's passed through the registration object
  return props.registration.documents?.filter((d: any) => d.verified_at) || []
})

const hasAnyHistory = computed(() => {
  return props.registration.verified_at || 
         props.registration.approved_at || 
         participantVerifications.value.length > 0 || 
         documentVerifications.value.length > 0
})

// Methods
const getVerificationIcon = (status: string) => {
  const icons: Record<string, string> = {
    'verified': 'checkCircle',
    'rejected': 'xCircle',
    'pending': 'clock'
  }
  return icons[status] || 'clock'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'verified': 'Terverifikasi',
    'rejected': 'Ditolak',
    'pending': 'Pending'
  }
  return labels[status] || status
}

const getDocumentStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    'approved': 'Disetujui',
    'rejected': 'Ditolak',
    'pending': 'Pending'
  }
  return labels[status] || status
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
